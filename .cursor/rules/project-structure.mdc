---
description: 
globs: 
alwaysApply: false
---
# 项目结构说明

本项目为 Kotlin Multiplatform（KMP）架构，核心代码位于 [composeApp/src](mdc:composeApp/src) 目录下，分为三大部分：

- [androidMain](mdc:composeApp/src/androidMain)：Android 平台专用代码，包括 AndroidManifest、平台相关工具类、入口 Activity 等。
- [iosMain](mdc:composeApp/src/iosMain)：iOS 平台专用代码，包括平台工具类、入口 ViewController 等。
- [commonMain](mdc:composeApp/src/commonMain)：跨平台核心代码，包含业务逻辑、UI、数据、网络、ViewModel 等，供 Android 和 iOS 共同调用。

## commonMain 主要结构
- [ui](mdc:composeApp/src/commonMain/kotlin/com/ymx/photovoltaic/ui)：界面相关代码，分为 page（页面）、widget（组件）、theme（主题）等。
- [data](mdc:composeApp/src/commonMain/kotlin/com/ymx/photovoltaic/data)：数据层，包含 bean（数据模型）、local（本地缓存）、response（接口响应模型）等。
- [http](mdc:composeApp/src/commonMain/kotlin/com/ymx/photovoltaic/http)：网络请求相关代码。
- [viewmodel](mdc:composeApp/src/commonMain/kotlin/com/ymx/photovoltaic/viewmodel)：各页面/模块的 ViewModel。
- [navigation](mdc:composeApp/src/commonMain/kotlin/com/ymx/photovoltaic/navigation)：导航路由相关代码。
- [config](mdc:composeApp/src/commonMain/kotlin/com/ymx/photovoltaic/config)：全局配置。
- [util](mdc:composeApp/src/commonMain/kotlin/com/ymx/photovoltaic/util)：工具类。
- [di](mdc:composeApp/src/commonMain/kotlin/com/ymx/photovoltaic/di)：依赖注入相关。
- [base](mdc:composeApp/src/commonMain/kotlin/com/ymx/photovoltaic/base)：基础 ViewModel 等基类。

## 平台相关代码
- Android 平台相关实现位于 [androidMain/kotlin/com/ymx/photovoltaic/platform](mdc:composeApp/src/androidMain/kotlin/com/ymx/photovoltaic/platform)
- iOS 平台相关实现位于 [iosMain/kotlin/com/ymx/photovoltaic/platform](mdc:composeApp/src/iosMain/kotlin/com/ymx/photovoltaic/platform)

## 入口文件
- Android 入口：[MainActivity.kt](mdc:composeApp/src/androidMain/kotlin/com/ymx/photovoltaic/MainActivity.kt)
- iOS 入口：[MainViewController.kt](mdc:composeApp/src/iosMain/kotlin/com/ymx/photovoltaic/MainViewController.kt)
- 跨平台 App 入口：[App.kt](mdc:composeApp/src/commonMain/kotlin/com/ymx/photovoltaic/App.kt)

如需查找具体功能模块，请优先在 commonMain 下查找，平台特有实现则在对应平台目录下。
