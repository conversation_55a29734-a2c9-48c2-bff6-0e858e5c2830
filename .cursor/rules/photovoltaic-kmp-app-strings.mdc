---
description: 
globs: 
alwaysApply: false
---
# 字符串国际化资源文件说明

本项目采用多语言国际化，所有字符串资源统一存放于 `composeApp/src/commonMain/composeResources/values/strings.xml`（中文） 和 `composeApp/src/commonMain/composeResources/values-en/strings.xml`（英文） 文件中。

## 文件位置
- 中文资源：[strings.xml](mdc:composeApp/src/commonMain/composeResources/values/strings.xml)
- 英文资源：[strings.xml](mdc:composeApp/src/commonMain/composeResources/values-en/strings.xml)

## 命名规范
- 所有字符串资源均使用小写加下划线命名，如 `optimizer_shutdown_switch`。
- 中文与英文资源文件中的 name 属性需保持一致。

## 如何引用
- 在KMP Compose代码中，需先导入：
  ```kotlin
  import org.jetbrains.compose.resources.stringResource
  import photovoltaic_kmp_app.composeapp.generated.resources.Res
  ```
- 使用方式：
  ```kotlin
  stringResource(Res.string.optimizer_shutdown_switch)
  ```

## 新增/修改流程
1. 先在中文和英文资源文件中分别添加/修改字符串。
2. 保证 name 属性一致，value 分别为中英文。
3. 在代码中通过 `stringResource` 统一引用。

## 注意事项
- 资源文件需保持同步，避免遗漏翻译。
- 资源 key 命名应简洁、语义明确。
