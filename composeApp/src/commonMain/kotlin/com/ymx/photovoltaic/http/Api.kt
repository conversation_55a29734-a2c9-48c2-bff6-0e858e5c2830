package com.ymx.photovoltaic.http

import com.ymx.photovoltaic.data.bean.Collector
import com.ymx.photovoltaic.data.bean.Group
import com.ymx.photovoltaic.data.bean.Optimizer
import com.ymx.photovoltaic.data.bean.PowerDayInfo
import com.ymx.photovoltaic.data.bean.Region
import com.ymx.photovoltaic.data.bean.Relay
import com.ymx.photovoltaic.data.bean.ScanDevice
import com.ymx.photovoltaic.data.bean.SearchGroup
import com.ymx.photovoltaic.data.bean.Station
import com.ymx.photovoltaic.data.bean.StationView
import com.ymx.photovoltaic.data.bean.TendDayInfo
import com.ymx.photovoltaic.data.bean.TendMinuteGroupInfo
import com.ymx.photovoltaic.data.bean.TendMinuteInfo
import com.ymx.photovoltaic.data.bean.User
import com.ymx.photovoltaic.data.bean.Version
import com.ymx.photovoltaic.data.bean.Warning
import com.ymx.photovoltaic.data.bean.WarningSettingModel
import com.ymx.photovoltaic.data.bean.WarningStats
import com.ymx.photovoltaic.data.bean.Weather
import com.ymx.photovoltaic.data.response.ApiResponse
import com.ymx.photovoltaic.data.response.ApiResponseWithNull
import com.ymx.photovoltaic.data.response.PageReModel
import com.ymx.photovoltaic.data.response.ReModel
import io.ktor.client.call.body
import io.ktor.client.request.forms.FormDataContent
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.Parameters
import io.ktor.http.contentType

/**
 * Http接口，Ktor实现
 */
object Api {
    // 使用函数获取BASE_URL，确保每次请求都使用最新的URL
    private fun getBaseUrl(): String = IpManager.getDefaultIP()

    /** 登录 */
    suspend fun login(
        username: String,
        pwd: String,
        registId: String
    ): ApiResponseWithNull<ReModel<User>> {
        return HttpClientManager.client.post("${getBaseUrl()}/member/login.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("phone", username)
                append("passwd", pwd)
                append("registId", registId)
            }))
        }.body()
    }

    /** 注册 */
    suspend fun register(
        phone: String,
        pwd: String,
        loginCode: String? = null,
        smsCode: String? = null
    ): ApiResponseWithNull<User> {
        return HttpClientManager.client.post("${getBaseUrl()}/member/registeredMember.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("phone", phone)
                append("passwd", pwd)
                loginCode?.let { append("loginCode", it) }
                smsCode?.let { append("smsCode", it) }
            }))
        }.body()
    }

    /** 修改密码 */
    suspend fun changePwd(
        userName: String,
        oldPwd: String,
        newPwd: String
    ): ApiResponseWithNull<String?> {
        return HttpClientManager.client.post("${getBaseUrl()}/member/changePasswd.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("phone", userName)
                append("passwd", oldPwd)
                append("confirmPasswd", newPwd)
            }))
        }.body()
    }

    /** 保存反馈 */
    suspend fun saveFeedback(
        information: String,
        content: String
    ): ApiResponse<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/appfeedback/savefeedback.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("information", information)
                append("content", content)
            }))
        }.body()
    }

    /** 电站列表 */
    suspend fun queryPowerStationList(
        mId: String,
        userType: String
    ): ApiResponse<PageReModel<List<Station>>> {
        return HttpClientManager.client.post("${getBaseUrl()}/powerStationAppCtr/queryPowerStationList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("mId", mId)
                append("userType", userType)
            }))
        }.body()
    }

    /** 新增电站 */
    suspend fun savePowerStationModel(
        stationMap: Map<String, String>
    ): ApiResponseWithNull<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/powerStationAppCtr/savePowerStationModel.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                stationMap.forEach { (key, value) ->
                    append(key, value)
                }
            }))
        }.body()
    }

    /** 系统视图 */
    suspend fun queryComponentList(
        powerStationId: String,
        date: String,
        type: String,
        sunUpTime: String,
        sunDownTime: String,
        from: String = "view",
        groupId: String?
    ): ApiResponse<StationView> {
        return HttpClientManager.client.post("${getBaseUrl()}/component/v1/queryComponentList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerStationId", powerStationId)
                append("date", date)
                append("type", type)
                append("sunUpTime", sunUpTime)
                append("sunDownTime", sunDownTime)
                append("from", from)
                groupId?.let { append("groupId", it) }
            }))
        }.body()
    }

    /** 组串视图 */
    suspend fun queryComponentGroupList(
        mId: String,
        userType: String
    ): ApiResponse<Station> {
        return HttpClientManager.client.post("${getBaseUrl()}/component/v1/queryComponentGroupList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("mId", mId)
                append("userType", userType)
            }))
        }.body()
    }

    /** 系统视图单独查询组件详情 */
    suspend fun queryComponentListByMinute(
        mId: String,
        userType: String
    ): ApiResponse<Station> {
        return HttpClientManager.client.post("${getBaseUrl()}/component/v1/queryComponentListByMinute.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("mId", mId)
                append("userType", userType)
            }))
        }.body()
    }

    /** 打开关闭组件 */
    suspend fun remoteControllerAck(
        powerStationId: String?,
        status: Int,
        chipId: String?,
        groupId: String?,
        flag: Int
    ): ApiResponseWithNull<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/zteCtr/remoteControllerAck.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                powerStationId?.let { append("powerStationId", it) }
                append("status", status.toString())
                append("flag", flag.toString())
                chipId?.let { append("id", it) }
                groupId?.let { append("belongsGroupId", it) }
            }))
        }.body()
    }

    /** 警报列表 */
    suspend fun queryWarningList(
        mId: String,
        pageNo: Int
    ): ApiResponse<PageReModel<List<Warning>>> {
        return HttpClientManager.client.post("${getBaseUrl()}/warningAppCtr/queryWarningList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("mId", mId)
                append("pageNo", pageNo.toString())
            }))
        }.body()
    }

    /** 警报详情 */
    suspend fun queryWarningById(
        mId: String,
        userType: String
    ): ApiResponse<Station> {
        return HttpClientManager.client.post("${getBaseUrl()}/warningAppCtr/v1/queryWarningById.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("mId", mId)
                append("userType", userType)
            }))
        }.body()
    }

    /** 统计分钟数据 */
    suspend fun queryReportByMinute(
        powerStationId: String,
        day: String
    ): ApiResponse<TendMinuteInfo> {
        return HttpClientManager.client.post("${getBaseUrl()}/statistics/queryComponentByMinute.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerStationId", powerStationId)
                append("day", day)
            }))
        }.body()
    }

    /** 统计分钟数据有Group */
    suspend fun queryReportByMinuteGroup(
        powerStationId: String,
        day: String,
        groupId: String,
        groupName: String
    ): ApiResponse<TendMinuteGroupInfo> {
        return HttpClientManager.client.post("${getBaseUrl()}/statistics/queryComponentByMinuteGroup.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerStationId", powerStationId)
                append("day", day)
                append("groupId", groupId)
                append("groupName", groupName)
            }))
        }.body()
    }

    /** 查询分组名称和id */
    suspend fun queryGroup(
        powerStationId: String,
        groupName: String?
    ): ApiResponse<ReModel<List<SearchGroup>>> {
        return HttpClientManager.client.post("${getBaseUrl()}/statistics/queryGroup.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerStationId", powerStationId)
                groupName?.let { append("groupName", it) }
            }))
        }.body()
    }

    /** 统计天数据 */
    suspend fun queryReportByDay(
        powerStationId: String,
        month: String
    ): ApiResponse<TendDayInfo> {
        return HttpClientManager.client.post("${getBaseUrl()}/statistics/queryComponentByDay.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerStationId", powerStationId)
                append("month", month)
            }))
        }.body()
    }

    /** 统计月数据 */
    suspend fun queryReportByMonth(
        powerStationId: String,
        year: String
    ): ApiResponse<TendDayInfo> {
        return HttpClientManager.client.post("${getBaseUrl()}/statistics/queryComponentByMonth.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerStationId", powerStationId)
                append("year", year)
            }))
        }.body()
    }

    /** 统计年数据 */
    suspend fun queryReportByYear(
        powerStationId: String
    ): ApiResponse<TendDayInfo> {
        return HttpClientManager.client.post("${getBaseUrl()}/statistics/queryComponentByYear.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerStationId", powerStationId)
            }))
        }.body()
    }

    /** 统计 当前功率，当天电量，总电流 */
    suspend fun queryTotalReport(
        powerStationId: String
    ): ApiResponse<PowerDayInfo> {
        return HttpClientManager.client.post("${getBaseUrl()}/statistics/queryComponentByStatistics.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerStationId", powerStationId)
            }))
        }.body()
    }

    /** 查询app 版本 */
    suspend fun appVersionList(
        versionNum: String,
        language: String
    ): ApiResponse<Version> {
        return HttpClientManager.client.post("${getBaseUrl()}/appVersion/appVersionList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("versionNum", versionNum)
                append("language", language)
            }))
        }.body()
    }

    /** 查询警报设置 */
    suspend fun queryWarningSettingList(
        powerStationId: String
    ): ApiResponse<WarningSettingModel> {
        return HttpClientManager.client.post("${getBaseUrl()}/warningSetting/queryWarningSettingList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerStationId", powerStationId)
            }))
        }.body()
    }

    /** 更新警报设置 */
    suspend fun updateWarningSettingList(
        powerStationId: String,
        componentTemperature: Int?,
        offValue: Int?,
        keepOutRate: Float?,
        keepOutTime: Int?
    ): ApiResponseWithNull<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/warningSetting/updateWarningSettingList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerStationId", powerStationId)
                componentTemperature?.let { append("componentTemperature", it.toString()) }
                offValue?.let { append("offValue", it.toString()) }
                keepOutRate?.let { append("keepOutRate", it.toString()) }
                keepOutTime?.let { append("keepOutTime", it.toString()) }
            }))
        }.body()
    }

    /** 查询电站详情 */
    suspend fun queryPowerStationById(
        powerStationId: String
    ): ApiResponse<Station> {
        return HttpClientManager.client.post("${getBaseUrl()}/powerStationAppCtr/queryPowerStationById.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("id", powerStationId)
            }))
        }.body()
    }

    /** 更新电站 */
    suspend fun updatePowerStationModel(
        stationMap: Map<String, String>
    ): ApiResponseWithNull<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/powerStationAppCtr/updatePowerStationModel.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                stationMap.forEach { (key, value) ->
                    append(key, value)
                }
            }))
        }.body()
    }

    /** 更新电站 */
    suspend fun queryCollectorList(
        powerStationId: String?
    ): ApiResponse<ReModel<List<Collector>>>{
        return HttpClientManager.client.post("${getBaseUrl()}/cloud/queryCloudTerminalList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                powerStationId?.let { append("powerStationId", it) }
            }))
        }.body()
    }


    /** 查看组串详情 */
    suspend fun viewGroup(
        id: String?
    ): ApiResponse<Group> {
        return HttpClientManager.client.post("${getBaseUrl()}/componentGroup/viewRequest.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                id?.let { append("id", it) }
            }))
        }.body()
    }

    /** 新增或修改组串 */
    suspend fun addOrModGroup(
        groupMap: Map<String, Any>
    ): ApiResponseWithNull<String> {
        return HttpClientManager.client.post("${getBaseUrl()}/componentGroup/saveOrUpdate.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                groupMap.forEach { (key, value) ->
                    append(key, value.toString())
                }
            }))
        }.body()
    }

    /** 查看优化器详情 */
    suspend fun viewOptimizer(
        id: String?
    ): ApiResponse<Optimizer> {
        return HttpClientManager.client.post("${getBaseUrl()}/component/viewRequest.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                id?.let { append("id", it) }
            }))
        }.body()
    }

    /** 新增或修改优化器 */
    suspend fun addOrModOptimizer(
        optimizerMap: Map<String, String>
    ): ApiResponseWithNull<String> {
        return HttpClientManager.client.post("${getBaseUrl()}/component/saveOrUpdate.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                optimizerMap.forEach { (key, value) ->
                    append(key, value)
                }
            }))
        }.body()
    }

    /** 新增或修改采集器 */
    suspend fun addOrModCollector(
        collectorMap: Map<String, String>
    ): ApiResponseWithNull<String> {
        return HttpClientManager.client.post("${getBaseUrl()}/cloud/saveOrUpdate.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                collectorMap.forEach { (key, value) ->
                    append(key, value)
                }
            }))
        }.body()
    }

    /** 查询组串列表 */
    suspend fun queryGroupList(
        cloudId: String?
    ): ApiResponse<ReModel<List<Group>>> {
        return HttpClientManager.client.post("${getBaseUrl()}/componentGroup/queryComponentGroupList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                cloudId?.let { append("cloudId", it) }
            }))
        }.body()
    }

    /** 查看优化器列表 */
    suspend fun queryOptimizerList(
        powerStationId: String?,
        belongsGroupId: String?,
        belongsGroupFlag: String?,
        pageSize: Int
    ): ApiResponse<ReModel<List<Optimizer>>> {
        return HttpClientManager.client.post("${getBaseUrl()}/component/queryComponentPageList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                powerStationId?.let { append("powerStationId", it) }
                belongsGroupId?.let { append("belongsGroupId", it) }
                belongsGroupFlag?.let { append("belongsGroupFlag", it) }
                append("pageSize", pageSize.toString())
            }))
        }.body()
    }

    /** 发送短信验证码 */
    suspend fun sendSms(
        account: String,
        type: String
    ): ApiResponseWithNull<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/sms/sendSms.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("account", account)
                append("type", type)
            }))
        }.body()
    }

    /** 重置密码 */
    suspend fun resetPassword(
        account: String,
        code: String,
        password: String,
        type: String
    ): ApiResponseWithNull<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/user/resetPassword.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("account", account)
                append("code", code)
                append("password", password)
                append("type", type)
            }))
        }.body()
    }

    /** 查询警告类型数量 */
    suspend fun queryWarningTypeCount(
        powerIdList: List<String>
    ): ApiResponse<WarningStats> {
        return HttpClientManager.client.post("${getBaseUrl()}/warningAppCtr/queryWarningTypeCount.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerIdList", powerIdList.joinToString(","))
            }))
        }.body()
    }

    /** 查询今日警告详情 */
    suspend fun queryTodayWarningDetails(
        powerIdList: List<String>
    ): ApiResponse<List<Warning>> {
        return HttpClientManager.client.post("${getBaseUrl()}/warningAppCtr/queryTodayWarningDetails.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerIdList", powerIdList.joinToString(","))
            }))
        }.body()
    }

    /** 查询地区信息 */
    suspend fun queryRegion(
        pid: Int,
        level: Int,
        language: String
    ): ApiResponse<List<Region>> {
        return HttpClientManager.client.post("${getBaseUrl()}/regionLocationAppCtr/queryRegion.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("pid", pid.toString())
                append("level", level.toString())
                append("language", language)
            }))
        }.body()
    }

    /** 获取天气信息 */
    suspend fun getWeather(
        districtId: String,
        dataType: String
    ): ApiResponse<Weather> {
        return HttpClientManager.client.post("${getBaseUrl()}/weather/getWeather.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("districtId", districtId)
                append("dataType", dataType)
            }))
        }.body()
    }

    /** 扫描二维码获取设备信息 */
    suspend fun getScanDeviceInfo(
        code: String,
        language: String? = null
    ): ApiResponse<ScanDevice> {
        return HttpClientManager.client.post("${getBaseUrl()}/zteCtr/getZteAck.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("code", code)
                language?.let { append("language", it) }
            }))
        }.body()
    }

    /** 查询中继列表 */
    suspend fun queryRelayList(
        createUserId: String,
        operationFlag: String,
        powerId: String
    ): ApiResponse<ReModel<List<Relay>>> {
        return HttpClientManager.client.post("${getBaseUrl()}/relay/queryRelayListForPowerStation.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("createUserId", createUserId)
                append("operationFlag", operationFlag)
                append("powerId", powerId)
            }))
        }.body()
    }

    /** 保存或更新中继 */
    suspend fun saveOrUpdateRelay(
        relayId: String,
        relayName: String,
        imei: String,
        powerStationId: String,
        createUserId: String,
        id: Int?
    ): ApiResponseWithNull<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/relay/saveOrUpdate.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("relayId", relayId)
                append("relayName", relayName)
                append("cloudId", imei)
                append("powerStationId", powerStationId)
                append("createUserId", createUserId)
                id?.let { append("id", it.toString()) }
            }))
        }.body()
    }

    /** 查询中继下的组件列表或可以添加的组件列表 operationFlag view| select */
    suspend fun queryRelayComponentList(
        relayId: String,
        pageNo: Int,
        pageSize: Int,
        operationFlag: String?,
        chipId: String?,
        groupName: String?,
        createUserId: String
    ): ApiResponse<ReModel<List<Optimizer>>> {
        return HttpClientManager.client.post("${getBaseUrl()}/relay/queryComponentList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("relayId", relayId)
                append("pageNo", pageNo.toString())
                append("pageSize", pageSize.toString())
                operationFlag?.let { append("operationFlag", it) }
                chipId?.let { append("chipId", it) }
                groupName?.let { append("groupName", it) }
                append("createUserId", createUserId)
            }))
        }.body()
    }

    /** 根据采集器ID查询中继列表 */
    suspend fun queryRelayListForCloudId(
        createUserId: String,
        operationFlag: String,
        cloudId: String
    ): ApiResponse<ReModel<List<Relay>>> {
        return HttpClientManager.client.post("${getBaseUrl()}/relay/queryRelayListForCloud.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("createUserId", createUserId)
                append("operationFlag", operationFlag)
                append("cloudId", cloudId)
            }))
        }.body()
    }

    /** 修改组件所属中继组 */
    suspend fun changeComponentGroup(
        id: String,
        relayId: String?,
        createUserId: String
    ): ApiResponseWithNull<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/relay/changeComponentGroup.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("id", id)
                relayId?.let { append("relayId", it) }
                append("createUserId", createUserId)
            }))
        }.body()
    }

    /** 修改组件所属中继 */
    suspend fun changeComponent(
        id: String,
        relayId: String?,
        createUserId: String
    ): ApiResponseWithNull<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/relay/changeComponent.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("id", id)
                relayId?.let { append("relayId", it) }
                append("createUserId", createUserId)
            }))
        }.body()
    }

    /** 查询中继下的组串列表 */
    suspend fun queryRelayGroupList(
        relayId: String,
        groupName: String,
        operationFlag: String,
        createUserId: String,
        pageSize: Int
    ): ApiResponse<ReModel<List<Group>>> {
        return HttpClientManager.client.post("${getBaseUrl()}/relay/queryComponentGroupList.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("relayId", relayId)
                append("groupName", groupName)
                append("operationFlag", operationFlag)
                append("createUserId", createUserId)
                append("pageSize", pageSize.toString())
            }))
        }.body()
    }
    
    /** 查询未读消息数量 */
    suspend fun queryUnreadMessageCount(
        powerIdList: List<String>
    ): ApiResponse<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/warningAppCtr/queryWarningUnreadCount.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("powerIdList", powerIdList.joinToString(","))
            }))
        }.body()
    }
    
    /** 设置消息为已读 */
    suspend fun setMessageRead(ids: String): ApiResponseWithNull<Int> {
        return HttpClientManager.client.post("${getBaseUrl()}/warningAppCtr/updateWarningReadStatus.api") {
            contentType(ContentType.Application.FormUrlEncoded)
            setBody(FormDataContent(Parameters.build {
                append("ids", ids)
            }))
        }.body()
    }
}