# 选择器统一样式迁移指南

## 概述

本次修改将所有弹出框统一成 `SelectorDialog` 的 `OperationSelector` 样式，替换了原有的 `WePicker` 样式。这样可以确保整个应用的选择器界面风格一致。

## 主要变化

### 1. 样式统一
- **之前**：使用 `WePicker` 样式（底部弹出，多列滚轮选择）
- **现在**：使用 `OperationSelector` 样式（居中弹出，卡片式界面，滚动选择）

### 2. 新增组件

#### MultiColumnOperationSelector
```kotlin
@Composable
fun MultiColumnOperationSelector(
    modifier: Modifier = Modifier,
    title: String = "选择",
    columns: List<List<String>>,
    initialSelectedIndices: List<Int> = List(columns.size) { 0 },
    onOptionSelected: (columnIndex: Int, optionIndex: Int, option: String) -> Unit = { _, _, _ -> },
    onCancelClick: () -> Unit = {},
    onConfirmClick: (List<Int>, List<String>) -> Unit = { _, _ -> }
)
```

#### 统一样式的选择器对话框
- `MultiColumnSelectorDialog` - 多列选择器
- `TimeSelectorDialog` - 时间选择器
- `RegionSelectorDialog` - 地区选择器

## 迁移示例

### 1. 时间选择器迁移

#### 之前的用法
```kotlin
val startTimePicker = rememberCustomTimePickerState()

// 使用
startTimePicker.show(LocalTime.parse(sunuptimeValue), type = TimeType.MINUTE) {
    sunuptimeValue = it.toString()
}
```

#### 现在的用法
```kotlin
val startTimePicker = rememberTimeSelectorDialogState()

// 使用
startTimePicker.show(
    value = LocalTime.parse(sunuptimeValue), 
    type = TimeType.MINUTE
) {
    sunuptimeValue = it.toString()
}
```

### 2. 地区选择器迁移

#### 之前的用法
```kotlin
val regionPicker = rememberRegionPickerState()

// 使用
regionPicker.show(
    title = selectedRegionTitle,
    values = regionValues
) { selected ->
    regionValues = selected
    // 处理选择结果...
}
```

#### 现在的用法
```kotlin
val regionPicker = rememberRegionSelectorDialogState()

// 使用（API 保持一致）
regionPicker.show(
    title = selectedRegionTitle,
    values = regionValues
) { selected ->
    regionValues = selected
    // 处理选择结果...
}
```

### 3. 简单选择器迁移

#### 之前的用法
```kotlin
val typePicker = rememberSingleColumnPickerState()

typePicker.show(
    title = selectStationType,
    range = listOf(type2_4g, typePlc),
    value = typeValue
) {
    typeValue = it
    typeText = if (it == 0) type2_4g else typePlc
}
```

#### 现在的用法
```kotlin
var showTypeSelector by remember { mutableStateOf(false) }

SelectorDialog(
    showDialog = showTypeSelector,
    title = selectStationType,
    options = listOf(type2_4g, typePlc),
    initialSelectedIndex = typeValue,
    onDismiss = { showTypeSelector = false },
    onOptionSelected = { index -> typeValue = index },
    onConfirm = { index, option ->
        typeValue = index
        typeText = option
        showTypeSelector = false
    }
)
```

## 优势

1. **视觉一致性**：所有选择器都使用相同的卡片式界面风格
2. **用户体验**：统一的交互方式，用户学习成本低
3. **维护性**：统一的组件架构，更容易维护和扩展
4. **自定义性**：更容易进行主题定制和样式调整

## 注意事项

1. **API 兼容性**：大部分 API 保持向后兼容，只需要更换状态管理函数
2. **样式差异**：新样式为居中弹出的卡片，而不是底部弹出的滚轮
3. **性能**：新的实现可能在某些复杂场景下性能略有差异

## 完整示例

参考 `UnifiedSelectorExample.kt` 文件查看完整的使用示例。
