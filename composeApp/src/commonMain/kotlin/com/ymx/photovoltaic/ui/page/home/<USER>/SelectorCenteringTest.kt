package com.ymx.photovoltaic.ui.page.home.edit

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.CustomInput
import com.ymx.photovoltaic.ui.page.widget.RegionDataManager
import com.ymx.photovoltaic.ui.page.widget.SelectorDialog
import com.ymx.photovoltaic.ui.page.widget.TimeType
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.rememberTimeSelectorDialogState
import com.ymx.photovoltaic.ui.page.widget.rememberRegionSelectorDialogState
import kotlinx.datetime.LocalTime
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.region
import photovoltaic_kmp_app.composeapp.generated.resources.save
import photovoltaic_kmp_app.composeapp.generated.resources.selected_region
import photovoltaic_kmp_app.composeapp.generated.resources.sunDowntime
import photovoltaic_kmp_app.composeapp.generated.resources.sunUptime

/**
 * 选择器居中测试页面
 * 用于测试选择器在选择后是否正确居中显示
 */
@Composable
fun SelectorCenteringTest(navHostController: NavHostController) {
    // 使用新的统一样式选择器状态管理
    val startTimePicker = rememberTimeSelectorDialogState()
    val endTimePicker = rememberTimeSelectorDialogState()
    val regionPicker = rememberRegionSelectorDialogState()
    
    // 获取字符串资源
    val selectedRegionTitle = stringResource(Res.string.selected_region)
    
    // 状态变量
    var regionValues by remember { mutableStateOf(arrayOf(0, 0, 0)) }
    var selectedRegionText by remember { mutableStateOf("请选择地区") }
    var sunuptimeValue by remember { mutableStateOf("06:00") }
    var sundowntimeValue by remember { mutableStateOf("18:00") }
    var numberValue by remember { mutableStateOf("50") }
    
    // 简单数字选择器状态
    var showNumberSelector by remember { mutableStateOf(false) }
    
    // 生成1-100的数字列表
    val numberOptions = remember { (1..100).map { it.toString() } }

    Scaffold(
        topBar = {
            TopBar("选择器居中测试", backClick = { navHostController.popBackStack() })
        },
        containerColor = Color(242, 243, 245, 255)
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 60.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, shape = RoundedCornerShape(8.dp))
                    .padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 20.dp)
            ) {
                // 简单数字选择器测试
                CustomInput(
                    value = numberValue,
                    label = "数字选择器测试",
                    placeholder = "",
                    onChange = { },
                    onClick = {
                        showNumberSelector = true
                    },
                    textAlign = TextAlign.Right
                )

                // 地区选择器测试 - 多列选择器
                CustomInput(
                    value = selectedRegionText,
                    label = stringResource(Res.string.region),
                    placeholder = "",
                    onChange = { },
                    onClick = {
                        regionPicker.show(
                            title = selectedRegionTitle,
                            values = regionValues
                        ) { selected ->
                            regionValues = selected
                            // 获取选中的地区对象
                            val region1 = RegionDataManager.getRegionsByLevel(2).getOrNull(selected[0])
                            val region2Children = RegionDataManager.getChildren(region1?.id ?: 0)
                            val region2 = if (region2Children.isEmpty()) null else region2Children.getOrNull(selected[1])
                            
                            val region3Children = RegionDataManager.getChildren(region2?.id ?: 0)
                            val region3 = if (region3Children.isEmpty()) null else region3Children.getOrNull(selected[2])
                            
                            selectedRegionText = "${region1?.name ?: ""} ${region2?.name ?: ""} ${region3?.name ?: ""}"
                        }
                    },
                    textAlign = TextAlign.Right
                )

                // 开始时间选择器测试 - 时间选择器
                CustomInput(
                    value = sunuptimeValue,
                    label = stringResource(Res.string.sunUptime),
                    placeholder = "",
                    onChange = { sunuptimeValue = it },
                    onClick = {
                        startTimePicker.show(
                            value = LocalTime.parse(sunuptimeValue), 
                            type = TimeType.MINUTE
                        ) {
                            sunuptimeValue = it.toString()
                        }
                    },
                    textAlign = TextAlign.Right
                )

                // 结束时间选择器测试 - 时间选择器
                CustomInput(
                    value = sundowntimeValue,
                    label = stringResource(Res.string.sunDowntime),
                    placeholder = "",
                    onChange = { sundowntimeValue = it },
                    onClick = {
                        endTimePicker.show(
                            value = LocalTime.parse(sundowntimeValue), 
                            type = TimeType.MINUTE
                        ) { selectedTime ->
                            sundowntimeValue = selectedTime.toString()
                        }
                    },
                    textAlign = TextAlign.Right
                )
            }

            // 数字选择器对话框 - 测试单列选择器的居中效果
            SelectorDialog(
                showDialog = showNumberSelector,
                title = "选择数字",
                options = numberOptions,
                initialSelectedIndex = numberOptions.indexOf(numberValue),
                onDismiss = { showNumberSelector = false },
                onOptionSelected = { index ->
                    // 实时更新选择
                },
                onConfirm = { index, option ->
                    numberValue = option
                    showNumberSelector = false
                }
            )

            Spacer(modifier = Modifier.weight(1f))

            ConfirmButton(stringResource(Res.string.save), true) {
                // 处理保存逻辑
                navHostController.popBackStack()
            }
        }
    }
}
