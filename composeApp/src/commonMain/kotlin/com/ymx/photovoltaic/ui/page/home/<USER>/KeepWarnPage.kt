package com.ymx.photovoltaic.ui.page.home.edit

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.CustomInput
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.rememberSingleColumnPickerState
import com.ymx.photovoltaic.viewmodel.EditViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.delay_shutdown
import photovoltaic_kmp_app.composeapp.generated.resources.save
import photovoltaic_kmp_app.composeapp.generated.resources.shade_ratio
import photovoltaic_kmp_app.composeapp.generated.resources.shade_warning
import photovoltaic_kmp_app.composeapp.generated.resources.unit_minute
import photovoltaic_kmp_app.composeapp.generated.resources.unit_percent
import kotlin.math.roundToInt


@Composable
fun KeepWarnPage(
    navHostController: NavHostController,
    editViewModel: EditViewModel = getKoin().get()
) {

    val warningSetting by editViewModel.warningSettingFlow.collectAsState()

    LaunchedEffect(Unit) {
        editViewModel.fetchWarningSetting(AppGlobal.powerStationId)
    }

    // 第一部分：顶部按钮
    var keepOutRateValue by remember { mutableStateOf(warningSetting?.keepOutRate.toString()) }
    var keepOutTimeValue by remember { mutableStateOf(warningSetting?.keepOutTime.toString()) }

    // warningSetting有变化时自动更新下面两个值
    LaunchedEffect(warningSetting) {
         keepOutRateValue=warningSetting?.keepOutRate?.roundToInt().toString()
         keepOutTimeValue=warningSetting?.keepOutTime.toString()
    }

    val picker = rememberSingleColumnPickerState()
    
    // 创建0-100的数值列表
    val ratioRange = remember {
        (1 ..100).map { it.toString() }
    }
    val timeRange = remember {
        (1 ..60).map { it.toString() }
    }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.shade_warning), backClick = {navHostController.popBackStack()})
        },
        backgroundColor = Grey_F5
    ) {
            paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues).padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 60.dp)
        ) {


            var showSuccessDialog by remember { mutableStateOf(false) }
            var showFailDialog by remember { mutableStateOf(false) }

            if (showSuccessDialog) {
                ResultDialog(true) { showSuccessDialog = false }
            }
            if (showFailDialog) {
                ResultDialog(false) { showFailDialog = false }
            }

            Spacer(modifier = Modifier.height(16.dp))

            Column(
                modifier = Modifier.fillMaxWidth()
                    .background(Color.White, shape = RoundedCornerShape(8.dp))
                    .padding(top=16.dp,start = 16.dp, end = 16.dp, bottom = 22.dp)
            ) {
                  val shadeRatio=stringResource(Res.string.shade_ratio)
                    CustomInput(
                        label = shadeRatio ,
                        value = "≥ ${keepOutRateValue}${stringResource(Res.string.unit_percent)}",
                        textAlign = TextAlign.Right,
                        labelWidth = 100.dp,
                        disabled = true,
                        onClick = {
                            picker.show(
                                title = shadeRatio,
                                range = ratioRange,
                                value = keepOutRateValue.toIntOrNull()?: 0
                            ) { selected ->
                                keepOutRateValue = ratioRange[selected]
                            }
                        }
                    )


                    val delayShutdown=stringResource(Res.string.delay_shutdown)
                    CustomInput(
                        label = delayShutdown ,
                        value = "≥ ${keepOutTimeValue}${stringResource(Res.string.unit_minute)}",
                        textAlign = TextAlign.Right,
                        labelWidth = 100.dp,
                        disabled = true,
                        onClick = {
                            picker.show(
                                title = delayShutdown,
                                range = timeRange,
                                value = keepOutTimeValue.toIntOrNull() ?: 0
                            ) { selected ->
                                keepOutTimeValue = timeRange[selected]
                            }
                        }
                    )
            }

            Spacer(modifier = Modifier.weight(1f))

            val isEnabled = keepOutRateValue.trim().isNotEmpty() && keepOutTimeValue.trim().isNotEmpty()
            val coroutineScope = rememberCoroutineScope()
            ConfirmButton(stringResource(Res.string.save), isEnabled) {

                editViewModel.updateWarningSettingList(AppGlobal.powerStationId, null,null,
                    keepOutRateValue.toFloat(), keepOutTimeValue.toInt(),
                    errorBlock = {
                        showFailDialog = true
                        coroutineScope.launch {
                            delay(2000)
                            showFailDialog = false
                        }
                    }
                ) {
                    showSuccessDialog = true
                    coroutineScope.launch {
                        delay(2000)
                        showSuccessDialog = false
                        navHostController.popBackStack()
                    }
                }
            }
        }
        }
    }


