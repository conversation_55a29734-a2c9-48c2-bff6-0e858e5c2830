package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.report_power_max
import photovoltaic_kmp_app.composeapp.generated.resources.report_power_now
import photovoltaic_kmp_app.composeapp.generated.resources.report_power_ratio

/**
 * 电站统计卡片组件，显示实时功率、功率比和装机功率
 * 
 * @param realTimePower 实时功率数值，如 "42.23"
 * @param realTimePowerUnit 实时功率单位，如 "kW"
 * @param powerRatio 功率比数值，如 "83.62"
 * @param powerRatioUnit 功率比单位，如 "%"
 * @param installedPower 装机功率数值，如 "50.5"
 * @param installedPowerUnit 装机功率单位，如 "kWp"
 * @param modifier 可选的修饰符
 */
@Composable
fun PowerStatsCard(
    realTimePower: String,
    realTimePowerUnit: String = "kW",
    powerRatio: String,
    powerRatioUnit: String = "%",
    installedPower: String,
    installedPowerUnit: String = "kWp",
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 10.dp, vertical = 5.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 30.dp, top = 12.dp, bottom = 12.dp),
            horizontalArrangement = Arrangement.Center
        ) {
            // 实时功率
            PowerStatItem(
                icon = painterResource(Res.drawable.report_power_now),
                title = "实时功率",
                value = realTimePower,
                unit = realTimePowerUnit,
                modifier = Modifier.weight(1f),
                iconSize = 35.dp
            )

            
            // 功率比
            PowerStatItem(
                icon = painterResource(Res.drawable.report_power_ratio),
                title = "功率占比",
                value = powerRatio,
                unit = powerRatioUnit,
                modifier = Modifier.weight(1f),
                iconSize = 35.dp
            )
            
            // 装机功率
            PowerStatItem(
                icon = painterResource(Res.drawable.report_power_max),
                title = "装机功率",
                value = installedPower,
                unit = installedPowerUnit,
                modifier = Modifier.weight(1f),
                iconSize = 35.dp
            )
        }
    }
}

/**
 * 电站统计项组件，显示图标、标题、数值和单位
 * 
 * @param icon 图标资源
 * @param title 标题文本
 * @param value 数值
 * @param unit 单位
 * @param modifier 可选的修饰符
 * @param iconSize 图标尺寸，默认36dp
 */
@Composable
private fun PowerStatItem(
    icon: androidx.compose.ui.graphics.painter.Painter,
    title: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier,
    iconSize: Dp = 32.dp
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 图标列
        Image(
            painter = icon,
            contentDescription = title,
            modifier = Modifier.size(iconSize)
        )
        
        Spacer(modifier = Modifier.width(6.dp))
        
        // 标题和数值列
        Column(
            horizontalAlignment = Alignment.Start
        ) {
            // 标题
            Text(
                text = title,
                style = TextStyle(
                    fontSize = 11.sp,
                    color = Color.Gray
                )
            )
            
            Spacer(modifier = Modifier.height(2.dp))
            
            // 数值和单位
            Row(
                verticalAlignment = Alignment.Bottom
            ) {
                Text(
                    text = value,
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black
                    )
                )
                Spacer(modifier = Modifier.width(2.dp))
                Text(
                    text = unit,
                    style = TextStyle(
                        fontSize = 11.sp,
                        color = Color.Gray
                    ),
                    modifier = Modifier.padding(bottom = 2.dp)
                )
            }
        }
    }
}