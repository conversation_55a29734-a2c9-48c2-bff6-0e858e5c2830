package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.datetime.LocalTime

/**
 * 选择器对话框组件
 *
 * @param showDialog 是否显示对话框
 * @param title 对话框标题
 * @param options 选项列表
 * @param initialSelectedIndex 初始选中的索引
 * @param onDismiss 关闭对话框的回调
 * @param onOptionSelected 选项选中时的回调
 * @param onConfirm 确认按钮点击时的回调
 */
@Composable
fun SelectorDialog(
    showDialog: <PERSON><PERSON><PERSON>,
    title: String,
    options: List<String>,
    initialSelectedIndex: Int,
    onDismiss: () -> Unit,
    onOptionSelected: (Int) -> Unit,
    onConfirm: (Int, String) -> Unit,
    boxContentAlignment: Alignment = Alignment.Center,
    selectModifier: Modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 24.dp)
) {
    if (showDialog) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = boxContentAlignment
            ) {
                OperationSelector(
                    title = title,
                    options = options,
                    initialSelectedIndex = initialSelectedIndex,
                    modifier = selectModifier,
                    onOptionSelected = { index, _ ->
                        onOptionSelected(index)
                    },
                    onCancelClick = onDismiss,
                    onConfirmClick = onConfirm
                )
            }
        }
    }
}

/**
 * 多列选择器对话框组件
 *
 * @param showDialog 是否显示对话框
 * @param title 对话框标题
 * @param columns 多列选项数据
 * @param initialSelectedIndices 初始选中的索引数组
 * @param onDismiss 关闭对话框的回调
 * @param onColumnValueChange 列值变化时的回调
 * @param onConfirm 确认按钮点击时的回调
 */
@Composable
fun MultiColumnSelectorDialog(
    showDialog: Boolean,
    title: String,
    columns: List<List<String>>,
    initialSelectedIndices: List<Int>,
    onDismiss: () -> Unit,
    onColumnValueChange: ((columnIndex: Int, optionIndex: Int, option: String) -> Unit)? = null,
    onConfirm: (List<Int>, List<String>) -> Unit
) {
    if (showDialog) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                MultiColumnOperationSelector(
                    title = title,
                    columns = columns,
                    initialSelectedIndices = initialSelectedIndices,
                    modifier = Modifier
                        .fillMaxWidth(0.85f)
                        .padding(horizontal = 24.dp),
                    onOptionSelected = { columnIndex, optionIndex, option ->
                        onColumnValueChange?.invoke(columnIndex, optionIndex, option)
                    },
                    onCancelClick = onDismiss,
                    onConfirmClick = { indices, options ->
                        onConfirm(indices, options)
                    }
                )
            }
        }
    }
}

/**
 * 时间选择器对话框组件
 *
 * @param showDialog 是否显示对话框
 * @param value 初始时间值
 * @param type 时间类型（小时、分钟、秒）
 * @param start 开始时间
 * @param end 结束时间
 * @param onDismiss 关闭对话框的回调
 * @param onConfirm 确认时间选择的回调
 */
@Composable
fun TimeSelectorDialog(
    showDialog: Boolean,
    value: LocalTime? = null,
    type: TimeType = TimeType.SECOND,
    start: LocalTime = LocalTime(0, 0, 0),
    end: LocalTime = LocalTime(23, 59, 59),
    onDismiss: () -> Unit,
    onConfirm: (LocalTime) -> Unit
) {
    if (showDialog) {
        val currentValue = value ?: LocalTime(12, 0, 0)

        // 生成时间选项
        val timeColumns = remember(start, end, type) {
            generateTimeColumns(start, end, type)
        }

        // 计算初始选中的索引
        val initialIndices = remember(currentValue, timeColumns, start, end, type) {
            calculateTimeIndices(currentValue, timeColumns, start, end, type)
        }

        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                MultiColumnOperationSelector(
                    title = "选择时间",
                    columns = timeColumns,
                    initialSelectedIndices = initialIndices,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp),
                    onCancelClick = onDismiss,
                    onConfirmClick = { indices, _ ->
                        val selectedTime = createTimeFromIndices(indices, timeColumns, start, end, type)
                        onConfirm(selectedTime)
                    }
                )
            }
        }
    }
}

/**
 * 地区选择器对话框组件
 *
 * @param showDialog 是否显示对话框
 * @param title 对话框标题
 * @param values 当前选中的地区索引数组
 * @param onDismiss 关闭对话框的回调
 * @param onConfirm 确认地区选择的回调
 */
@Composable
fun RegionSelectorDialog(
    showDialog: Boolean,
    title: String? = null,
    values: Array<Int>,
    onDismiss: () -> Unit,
    onConfirm: (Array<Int>) -> Unit
) {
    if (showDialog) {
        // 使用状态来管理当前选中的值，以支持级联更新
        var currentValues by remember { mutableStateOf(values.toList()) }

        // 生成地区选项
        val regionColumns = remember(currentValues) {
            generateRegionColumns(currentValues.toTypedArray())
        }

        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                MultiColumnOperationSelector(
                    title = title ?: "选择地区",
                    columns = regionColumns,
                    initialSelectedIndices = currentValues,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 24.dp),
                    onOptionSelected = { columnIndex, optionIndex, _ ->
                        // 当选择上级地区时，重置下级地区
                        currentValues = when (columnIndex) {
                            0 -> listOf(optionIndex, 0, 0) // 省份变化，重置市和区
                            1 -> listOf(currentValues[0], optionIndex, 0) // 市变化，重置区
                            2 -> listOf(currentValues[0], currentValues[1], optionIndex) // 区变化
                            else -> currentValues
                        }
                    },
                    onCancelClick = onDismiss,
                    onConfirmClick = { indices, _ ->
                        onConfirm(indices.toTypedArray())
                    }
                )
            }
        }
    }
}

/**
 * 生成时间选择器的列数据
 */
private fun generateTimeColumns(start: LocalTime, end: LocalTime, type: TimeType): List<List<String>> {
    val columns = mutableListOf<List<String>>()

    // 小时列
    val hours = (start.hour..end.hour).map { it.toString().padStart(2, '0') }
    columns.add(hours)

    // 分钟列
    if (type == TimeType.MINUTE || type == TimeType.SECOND) {
        val minutes = (0..59).map { it.toString().padStart(2, '0') }
        columns.add(minutes)
    }

    // 秒列
    if (type == TimeType.SECOND) {
        val seconds = (0..59).map { it.toString().padStart(2, '0') }
        columns.add(seconds)
    }

    return columns
}

/**
 * 计算时间的初始选中索引
 */
private fun calculateTimeIndices(
    currentValue: LocalTime,
    timeColumns: List<List<String>>,
    start: LocalTime,
    end: LocalTime,
    type: TimeType
): List<Int> {
    val indices = mutableListOf<Int>()

    // 小时索引
    val hourIndex = currentValue.hour - start.hour
    indices.add(hourIndex.coerceIn(0, timeColumns[0].size - 1))

    // 分钟索引
    if (type == TimeType.MINUTE || type == TimeType.SECOND) {
        indices.add(currentValue.minute.coerceIn(0, timeColumns[1].size - 1))
    }

    // 秒索引
    if (type == TimeType.SECOND) {
        indices.add(currentValue.second.coerceIn(0, timeColumns[2].size - 1))
    }

    return indices
}

/**
 * 从索引创建时间对象
 */
private fun createTimeFromIndices(
    indices: List<Int>,
    timeColumns: List<List<String>>,
    start: LocalTime,
    end: LocalTime,
    type: TimeType
): LocalTime {
    val hour = if (indices.isNotEmpty()) {
        timeColumns[0][indices[0]].toInt()
    } else start.hour

    val minute = if (indices.size > 1 && (type == TimeType.MINUTE || type == TimeType.SECOND)) {
        timeColumns[1][indices[1]].toInt()
    } else 0

    val second = if (indices.size > 2 && type == TimeType.SECOND) {
        timeColumns[2][indices[2]].toInt()
    } else 0

    return LocalTime(hour, minute, second)
}

/**
 * 生成地区选择器的列数据
 */
private fun generateRegionColumns(values: Array<Int>): List<List<String>> {
    val level2Regions = RegionDataManager.getRegionsByLevel(2)
    val level3Regions = if (values[0] >= 0 && values[0] < level2Regions.size) {
        RegionDataManager.getChildren(level2Regions[values[0]].id)
    } else emptyList()
    val level4Regions = if (values[1] >= 0 && values[1] < level3Regions.size) {
        RegionDataManager.getChildren(level3Regions[values[1]].id)
    } else emptyList()

    return listOf(
        level2Regions.map { it.name },
        level3Regions.map { it.name },
        level4Regions.map { it.name }
    )
}