package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.datetime.LocalTime

/**
 * 选择器对话框组件
 *
 * @param showDialog 是否显示对话框
 * @param title 对话框标题
 * @param options 选项列表
 * @param initialSelectedIndex 初始选中的索引
 * @param onDismiss 关闭对话框的回调
 * @param onOptionSelected 选项选中时的回调
 * @param onConfirm 确认按钮点击时的回调
 */
@Composable
fun SelectorDialog(
    showDialog: <PERSON><PERSON><PERSON>,
    title: String,
    options: List<String>,
    initialSelectedIndex: Int,
    onDismiss: () -> Unit,
    onOptionSelected: (Int) -> Unit,
    onConfirm: (Int, String) -> Unit,
    boxContentAlignment: Alignment = Alignment.Center,
    selectModifier: Modifier = Modifier
        .fillMaxWidth()
        .padding(horizontal = 24.dp)
) {
    if (showDialog) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = boxContentAlignment
            ) {
                OperationSelector(
                    title = title,
                    options = options,
                    initialSelectedIndex = initialSelectedIndex,
                    modifier = selectModifier,
                    onOptionSelected = { index, _ ->
                        onOptionSelected(index)
                    },
                    onCancelClick = onDismiss,
                    onConfirmClick = onConfirm
                )
            }
        }
    }
}

/**
 * 多列选择器对话框组件
 *
 * @param showDialog 是否显示对话框
 * @param title 对话框标题
 * @param ranges 多列选项数组
 * @param values 当前选中的索引数组
 * @param onDismiss 关闭对话框的回调
 * @param onColumnValueChange 列值变化时的回调
 * @param onConfirm 确认按钮点击时的回调
 */
@Composable
fun MultiColumnSelectorDialog(
    showDialog: Boolean,
    title: String,
    ranges: Array<List<String>>,
    values: Array<Int>,
    onDismiss: () -> Unit,
    onColumnValueChange: ((column: Int, value: Int, values: Array<Int>) -> Unit)? = null,
    onConfirm: (Array<Int>) -> Unit
) {
    if (showDialog) {
        WePicker(
            visible = showDialog,
            ranges = ranges,
            values = values,
            title = title,
            onCancel = onDismiss,
            onColumnValueChange = onColumnValueChange,
            onValuesChange = onConfirm
        )
    }
}

/**
 * 时间选择器对话框组件
 *
 * @param showDialog 是否显示对话框
 * @param value 初始时间值
 * @param type 时间类型（小时、分钟、秒）
 * @param start 开始时间
 * @param end 结束时间
 * @param onDismiss 关闭对话框的回调
 * @param onConfirm 确认时间选择的回调
 */
@Composable
fun TimeSelectorDialog(
    showDialog: Boolean,
    value: LocalTime? = null,
    type: TimeType = TimeType.SECOND,
    start: LocalTime = LocalTime(0, 0, 0),
    end: LocalTime = LocalTime(23, 59, 59),
    onDismiss: () -> Unit,
    onConfirm: (LocalTime) -> Unit
) {
    if (showDialog) {
        WeTimePicker(
            visible = showDialog,
            value = value,
            type = type,
            start = start,
            end = end,
            onCancel = onDismiss,
            onChange = onConfirm
        )
    }
}

/**
 * 地区选择器对话框组件
 *
 * @param showDialog 是否显示对话框
 * @param title 对话框标题
 * @param values 当前选中的地区索引数组
 * @param onDismiss 关闭对话框的回调
 * @param onConfirm 确认地区选择的回调
 */
@Composable
fun RegionSelectorDialog(
    showDialog: Boolean,
    title: String? = null,
    values: Array<Int>,
    onDismiss: () -> Unit,
    onConfirm: (Array<Int>) -> Unit
) {
    if (showDialog) {
        WeRegionPicker(
            visible = showDialog,
            title = title,
            values = values,
            onCancel = onDismiss,
            onConfirm = onConfirm
        )
    }
}