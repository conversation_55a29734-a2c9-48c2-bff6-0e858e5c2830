package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties

/**
 * 选择器对话框组件
 * 
 * @param showDialog 是否显示对话框
 * @param title 对话框标题
 * @param options 选项列表
 * @param initialSelectedIndex 初始选中的索引
 * @param onDismiss 关闭对话框的回调
 * @param onOptionSelected 选项选中时的回调
 * @param onConfirm 确认按钮点击时的回调
 */
@Composable
fun SelectorDialog(
    showDialog: Boolean,
    title: String,
    options: List<String>,
    initialSelectedIndex: Int,
    onDismiss: () -> Unit,
    onOptionSelected: (Int) -> Unit,
    onConfirm: (Int, String) -> Unit,
    boxContentAlignment:Alignment=Alignment.Center,
    selectModifier: Modifier= Modifier
        .fillMaxWidth()
        .padding(horizontal = 24.dp)
) {
    if (showDialog) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = boxContentAlignment
            ) {
                OperationSelector(
                    title = title,
                    options = options,
                    initialSelectedIndex = initialSelectedIndex,
                    modifier = selectModifier,
                    onOptionSelected = { index, _ ->
                        onOptionSelected(index)
                    },
                    onCancelClick = onDismiss,
                    onConfirmClick = onConfirm
                )
            }
        }
    }
}