package com.ymx.photovoltaic.ui.page.my

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.config.AppConfig
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.data.local.CacheManager
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.ui.page.common.CommonBottomBar
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.MenuItem
import com.ymx.photovoltaic.util.encodeUrl
import com.ymx.photovoltaic.viewmodel.HomeViewModel
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.change_password
import photovoltaic_kmp_app.composeapp.generated.resources.contact_us
import photovoltaic_kmp_app.composeapp.generated.resources.feedback
import photovoltaic_kmp_app.composeapp.generated.resources.logout
import photovoltaic_kmp_app.composeapp.generated.resources.my_contact
import photovoltaic_kmp_app.composeapp.generated.resources.my_exit
import photovoltaic_kmp_app.composeapp.generated.resources.my_feedback
import photovoltaic_kmp_app.composeapp.generated.resources.my_head
import photovoltaic_kmp_app.composeapp.generated.resources.my_manual
import photovoltaic_kmp_app.composeapp.generated.resources.my_message_read
import photovoltaic_kmp_app.composeapp.generated.resources.my_message_unread
import photovoltaic_kmp_app.composeapp.generated.resources.my_password
import photovoltaic_kmp_app.composeapp.generated.resources.my_set
import photovoltaic_kmp_app.composeapp.generated.resources.my_test
import photovoltaic_kmp_app.composeapp.generated.resources.my_verification
import photovoltaic_kmp_app.composeapp.generated.resources.sampling_test
import photovoltaic_kmp_app.composeapp.generated.resources.settings
import photovoltaic_kmp_app.composeapp.generated.resources.user_manual
import photovoltaic_kmp_app.composeapp.generated.resources.user_role
import photovoltaic_kmp_app.composeapp.generated.resources.verify_authenticity


@Composable
fun MyPage(
    navHostController: NavHostController,
    homeViewModel: HomeViewModel = getKoin().get()
) {

    // 将 stringResource 调用移到这里
    val contactUsTitle = stringResource(Res.string.contact_us)
    val userManualTitle = stringResource(Res.string.user_manual)
    
    // 在页面加载时请求未读消息数量
    LaunchedEffect(Unit) {
        AppGlobal.powerIdList?.let { powerIdList ->
            homeViewModel.fetchUnreadMessageCount(powerIdList)
        }
    }

    Scaffold(
        containerColor = Grey_F5,
        topBar = {
            // 用户信息区域
            UserInfoSection(navHostController, homeViewModel)
        },
        bottomBar = {
            CommonBottomBar(navController = navHostController)
        }
    ){
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(it)
            .verticalScroll(rememberScrollState())
    ) {

        Spacer(modifier = Modifier.height(20.dp))

        // 菜单项区域
        Column(
            modifier = Modifier.padding(start = 10.dp, end = 10.dp)
        ) {
            MenuItem(icon = painterResource( Res.drawable.my_set), text = stringResource(Res.string.settings),
                onItemClick = { navHostController.navigate(Route.SETTING) })
            val userType = CacheManager.getUser()?.type ?: 0
            val  userName= CacheManager.getUser()?.nickName?:""

            if (userType != 1) {
         MenuItem(icon = painterResource( Res.drawable.my_verification), text = stringResource(Res.string.verify_authenticity),
            onItemClick = { navHostController.navigate(Route.VERIFY_SCAN) })
                if(userName=="ljjx"||userName=="ysoper")
                {
                    MenuItem(icon = painterResource( Res.drawable.my_test), text = stringResource(Res.string.sampling_test),
                        onItemClick = { navHostController.navigate(Route.SAMPLING_TEST) })
                }
            }

            MenuItem(
                icon = painterResource( Res.drawable.my_contact),
                text = contactUsTitle,
                onItemClick = {
                    val contactUrl = AppConfig.getContactInfoUrl()
                    val encodedUrl = encodeUrl(contactUrl)
                    val encodedTitle = encodeUrl(contactUsTitle)
                    navHostController.navigate("web_view?title=$encodedTitle&url=$encodedUrl")
                }
            )
            MenuItem(
                icon = painterResource( Res.drawable.my_manual),
                text = userManualTitle,
                onItemClick = {
                    val manualUrl = AppConfig.getUserManualUrl()
                    val encodedUrl = encodeUrl(manualUrl)
                    val encodedTitle = encodeUrl(userManualTitle)
                    navHostController.navigate("web_view?title=$encodedTitle&url=$encodedUrl")
                }
            )
            MenuItem(icon = painterResource( Res.drawable.my_feedback) , text = stringResource(Res.string.feedback),
                onItemClick = { navHostController.navigate(Route.FEEDBACK) })
            MenuItem(icon =painterResource( Res.drawable.my_password), text = stringResource(Res.string.change_password),
                onItemClick = { navHostController.navigate(Route.CHANGE_PASSWORD) })
            MenuItem(icon = painterResource( Res.drawable.my_exit), text = stringResource(Res.string.logout),
                onItemClick = { navHostController.navigate(Route.LOGIN) },
                isShowExitDialog = true)
        }
    }
    }
}

@Composable
fun UserInfoSection(navHostController: NavHostController, homeViewModel: HomeViewModel) {
    // 获取未读消息数量
    val unreadMessageCount by homeViewModel.unreadMessageCountFlow.collectAsState()
    
    Row(
        modifier = Modifier
            .fillMaxWidth().height(110.dp)
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFFFEB9A9),// rgba(255, 87, 51, 0.5)
                        Color(0x00FFFFFF)  // rgba(255, 255, 255, 0)
                    ),
                    startY = 0f,
                    endY = Float.POSITIVE_INFINITY
                )
            ).statusBarsPadding()
    ) {
    }
    
    // 将用户信息部分移到Box外部
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 18.dp,end=16.dp,top=95.dp, bottom = 10.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 用户头像
        Image(
            painter = painterResource( Res.drawable.my_head), // 替换为你的头像资源
            contentDescription = "User Avatar",
            modifier = Modifier
                .size(64.dp)
                .clip(CircleShape),
            contentScale = ContentScale.Crop
        )

        Spacer(modifier = Modifier.width(16.dp))
        val user=CacheManager.getUser()
        Column {
            if (user != null) {
                Row(verticalAlignment = Alignment.CenterVertically)
                {
                    Text(text = user.nickName, fontSize = 28.sp, fontWeight = FontWeight.Bold,
                        modifier = Modifier.padding( end = 5.dp))
                    when(user.type){
                        1-> { UserRole(stringResource(Res.string.user_role)) }
                        2-> { UserRole("运维管理员")}
                        3-> {UserRole("超级管理员")}
                    }
                }
            }
            Spacer(modifier = Modifier.height(4.dp))

            Text(text = "公司 > 运维组", fontSize = 12.sp, color = Color.Gray)
        }

        Spacer(modifier = Modifier.weight(1f))

        // 根据未读消息数量显示不同图标
        Image(
            painter = painterResource(
                if (unreadMessageCount > 0) Res.drawable.my_message_unread 
                else Res.drawable.my_message_read
            ),
            contentDescription = "Message Icon",
            modifier = Modifier
                .size(40.dp).clickable
                {
                    navHostController.navigate(Route.MESSAGE) }
                ,
        )
        Spacer(modifier = Modifier.width(10.dp))
    }
}

@Composable
fun UserRole(role:String){

    Box(
        modifier = Modifier.padding(top = 2.dp)
            .clip(RoundedCornerShape(25.dp))
            .background(Color(39,90,219,20))
            .border(1.dp, Color(39,90,219,200), RoundedCornerShape(25.dp))
            .padding(horizontal = 6.dp, vertical = 6.dp)
    ) {
        Text(
            text = role,
            color = Color(39,90,219,200),
            fontSize = 12.sp
        )
    }
}
