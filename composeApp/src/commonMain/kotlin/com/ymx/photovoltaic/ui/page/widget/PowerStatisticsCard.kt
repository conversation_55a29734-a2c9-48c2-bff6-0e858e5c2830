package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.LightGreen
import com.ymx.photovoltaic.ui.page.theme.LightYellow
import org.jetbrains.compose.resources.DrawableResource
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.report_day_income
import photovoltaic_kmp_app.composeapp.generated.resources.report_day_power

/**
 * 电站发电和收益统计卡片
 *
 * @param modifier 自定义修饰符
 * @param dayIncome 当日收益
 * @param monthIncome 当月收益
 * @param yearIncome 当年收益
 * @param totalIncome 累计收益
 * @param dayPower 当日发电量
 * @param monthPower 当月发电量
 * @param yearPower 当年发电量
 * @param totalPower 累计发电量
 */
@Composable
fun PowerStatisticsCard(
    modifier: Modifier = Modifier,
    dayIncome: String,
    monthIncome: String,
    yearIncome: String,
    totalIncome: String,
    dayPower: String,
    monthPower: String,
    yearPower: String,
    totalPower: String
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 10.dp, vertical = 5.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // 收益统计
            StatisticsRow(
                iconRes = Res.drawable.report_day_income,
                title = "当日收益 (元)",
                dayValue = dayIncome,
                monthValue = monthIncome,
                yearValue = yearIncome,
                totalValue = totalIncome,
                backgroundColor = LightYellow,
                unitText = "(元)"
            )

            // 发电统计
            StatisticsRow(
                iconRes = Res.drawable.report_day_power,
                title = "当日发电 (kWh)",
                dayValue = dayPower,
                monthValue = monthPower,
                yearValue = yearPower,
                totalValue = totalPower,
                backgroundColor = LightGreen,
                unitText = "(kWh)"
            )
        }
    }
}

@Composable
private fun StatisticsRow(
    iconRes: DrawableResource,
    title: String,
    dayValue: String,
    monthValue: String,
    yearValue: String,
    totalValue: String,
    backgroundColor: Color,
    unitText: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 当日数值部分（有背景色）
        Box(
            modifier = Modifier
                .weight(1.8f)
                .padding(start = 6.dp, top = 6.dp, bottom = 6.dp)
                .clip(RoundedCornerShape(15.dp))
                .background(backgroundColor)
                .padding(start = 6.dp, end = 2.dp, top = 6.dp, bottom = 6.dp)
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                // 图标
                Image(
                    painter = painterResource(iconRes),
                    contentDescription = title,
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(6.dp))
                
                // 当日数值部分
                Column {
                    Text(
                        text = title,
                        style = TextStyle(
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    )
                    
                    Text(
                        text = dayValue,
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )
                    )
                }
            }
        }
        
        // 其他统计数据（白色背景）
        Row(
            modifier = Modifier
                .weight(3.2f)
                .background(Color.White)
                .padding(vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val monthLabelText= if(title=="当日收益 (元)")"当月收益"
            else "当月发电"
            // 当月数据
            StatisticsItemColumn(
                label = "$monthLabelText${unitText}",
                value = monthValue,
                modifier = Modifier.weight(1f)
            )
            
            // 分隔线
            Spacer(
                modifier = Modifier
                    .width(1.dp)
                    .height(34.dp)
                    .background(Color(0xFFEEEEEE))
            )

            val yearLabelText= if(title=="当日收益 (元)")"当年收益"
            else "当年发电"
            // 当年数据
            StatisticsItemColumn(
                label = "$yearLabelText${unitText}",
                value = yearValue,
                modifier = Modifier.weight(1f)
            )
            
            // 分隔线
            Spacer(
                modifier = Modifier
                    .width(1.dp)
                    .height(34.dp)
                    .background(Color(0xFFEEEEEE))
            )

            val totalLabelText= if(title=="当日收益 (元)")"累计收益"
            else "累计发电"
            // 累计数据
            StatisticsItemColumn(
                label = "$totalLabelText${unitText}",
                value = totalValue,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun StatisticsItemColumn(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Text(
            text = label,
            style = TextStyle(
                fontSize = 10.sp,
                color = Color.Gray
            )
        )
        
        Spacer(modifier = Modifier.height(2.dp))
        
        Text(
            text = value,
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold
            )
        )
    }
}

/**
 *
 */
@Composable
fun PowerStatisticsCardExample() {
    PowerStatisticsCard(
        dayIncome = "92.5",
        monthIncome = "4,429.1",
        yearIncome = "15,400",
        totalIncome = "129,600",
        dayPower = "94.9",
        monthPower = "4,431.5",
        yearPower = "15,400",
        totalPower = "257,000"
    )
} 