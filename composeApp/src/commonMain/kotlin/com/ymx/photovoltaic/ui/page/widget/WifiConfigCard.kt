package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.jetbrains.compose.resources.painterResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.collector_config_wifi
import photovoltaic_kmp_app.composeapp.generated.resources.ic_pwd_hide
import photovoltaic_kmp_app.composeapp.generated.resources.ic_pwd_show
import photovoltaic_kmp_app.composeapp.generated.resources.please_input

@Composable
fun WifiConfigCard(
    modifier: Modifier = Modifier,
    // backgroundPainter: Painter, // 取消注释并传入你的背景图片 Painter
    onJoinClick: (ssid: String, password: String) -> Unit
) {
    var ssid by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var isPasswordVisible by remember { mutableStateOf(false) }

    Card(
        modifier = modifier
            .fillMaxWidth(),
//            .padding(12.dp),  // 减小外边距
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
       colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5)) 
// 近似背景色
    ) {
        // Box for background image if needed
         Box {
            //  Image(
            //      painter = painterResource(Res.drawable.set_collector), // 使用传入的背景 Painter
            //      contentDescription = "Background",
            //      contentScale = ContentScale.Crop, // Or FillBounds, etc.
            //      modifier = Modifier.matchParentSize()
            //  )

            Column(
                modifier = Modifier
                    .padding(horizontal = 24.dp, vertical = 24.dp)  // 减小垂直内边距
                    .fillMaxWidth()
            ) {
                Row(
                    verticalAlignment = Alignment.Top // Align icon to the top part of the row
                ) {
                   Icon(
                       painter = painterResource(Res.drawable.collector_config_wifi),
                       contentDescription = "WiFi Icon",
                       modifier = Modifier.size(50.dp).padding(end = 16.dp),  // 减小图标大小
                       tint = Color.Gray
                   )

                    Column(
                        modifier = Modifier.weight(1f) // Take remaining space
                    ) {
                        // WiFi 名称输入行
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Text(
                                "WiFi名称",
                                modifier = Modifier.width(70.dp),
                                fontSize = 14.sp,
                                color = Color.DarkGray
                            )
                            InputWithCenteredPlaceholder(
                                value = ssid,
                                onValueChange = { ssid = it },
                                modifier = Modifier
                                    .weight(1f)
                                    .height(42.dp)
                                    .clip(RoundedCornerShape(20.dp))
                            )
                        }

                        Spacer(modifier = Modifier.height(12.dp))  // 减小间距

                        // WiFi 密码输入行
                        Row(verticalAlignment = Alignment.CenterVertically) {
                             Text(
                                 "WiFi密码",
                                 modifier = Modifier.width(70.dp),
                                 fontSize = 14.sp,
                                 color = Color.DarkGray
                             )
                            InputWithCenteredPlaceholder(
                                value = password,
                                onValueChange = { password = it },
                                modifier = Modifier
                                    .weight(1f)
                                    .height(40.dp)
                                    .clip(RoundedCornerShape(20.dp)),
                                visualTransformation = if (isPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                                trailingIcon = {
                                    IconButton(onClick = { isPasswordVisible = !isPasswordVisible }) {
                                        Icon(
                                            painter = painterResource(if (isPasswordVisible) Res.drawable.ic_pwd_show else Res.drawable.ic_pwd_hide),
                                            contentDescription = "Toggle password visibility", 
                                            tint = Color.Gray,
                                            modifier = Modifier.size(20.dp)
                                        )
                                    }
                                }
                            )
                        }

                        Spacer(modifier = Modifier.height(24.dp))  // 减小间距

                        // 加入按钮 (Moved inside the Column)
                        ConfirmButton(
                            showText = "加入",
                            enabledBoolean = ssid.isNotEmpty() && password.isNotEmpty(),
                            onItemClick = { onJoinClick(ssid, password) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun InputWithCenteredPlaceholder(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    trailingIcon: @Composable (() -> Unit)? = null,
    visualTransformation: VisualTransformation = VisualTransformation.None
) {
    // 定义文本样式
    val textStyle = TextStyle(
        color = Color.Black,
        textAlign = TextAlign.Left,
        fontSize = 13.sp,
        lineHeight = 18.sp  // 增加行高以确保文本完整显示
    )
    
    // 使用BasicTextField并自定义装饰
    BasicTextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier,
        textStyle = textStyle,
        singleLine = true,
        visualTransformation = visualTransformation,
        decorationBox = { innerTextField ->
            // 创建自定义装饰框
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.White, RoundedCornerShape(15.dp))
                    .padding(horizontal = 12.dp, vertical = 8.dp) // 内边距确保内容不贴边
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxSize()
                ) {
                    // 文本字段和占位符的容器
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart // 确保内容左对齐且垂直居中
                    ) {
                        innerTextField()
                        
                        // 如果文本框中没有输入内容，则显示占位文本
                        if (value.isEmpty()) {
                            PlaceholderText(
                                textResId = Res.string.please_input,
                                textLineHeight = 13.sp,
                                textFontSize = 13.sp,
                                textAlign = TextAlign.Left
                            )
                        }
                    }
                    
                    // 尾部图标
                    if (trailingIcon != null) {
                        trailingIcon()
                    }
                }
            }
        }
    )
}