# 选择器自动居中修复

## 问题描述

在使用统一样式的选择器时，发现以下问题：
1. 当选择区域时，选择之后区域没有自动显示在两条横线的中间
2. 选择时间也有同样的问题
3. 用户拖动时停在哪就显示在哪，没有自动吸附到中心位置

## 问题原因

原来的实现中，选择器只是在滚动过程中计算中心项，但没有在滚动停止后自动调整到中心位置。这导致用户手动滚动后，选中的项可能不在两条横线的中间。

## 修复方案

### 1. 修复 `SingleColumnList` 组件

在 `OperationSelector.kt` 中的 `SingleColumnList` 组件添加了自动居中功能：

```kotlin
// 当用户停止滚动时，自动滚动到最近的中心项
LaunchedEffect(listState.isScrollInProgress) {
    if (!listState.isScrollInProgress) {
        // 滚动停止时，确保选中项居中显示
        val targetIndex = centerItemIndex + 1 // +1 是因为有顶部空白项
        if (targetIndex >= 1 && targetIndex <= options.size) {
            listState.animateScrollToItem(targetIndex)
        }
    }
}
```

### 2. 修复 `OperationSelector` 组件

在原始的 `OperationSelector` 组件中也添加了相同的自动居中功能：

```kotlin
// 当用户停止滚动时，自动滚动到最近的中心项
LaunchedEffect(listState.isScrollInProgress) {
    if (!listState.isScrollInProgress) {
        // 滚动停止时，确保选中项居中显示
        val targetIndex = centerItemIndex + 1 // +1 是因为有顶部空白项
        if (targetIndex >= 1 && targetIndex <= options.size) {
            listState.animateScrollToItem(targetIndex)
        }
    }
}
```

### 3. 修复 `MultiColumnOperationSelector` 组件

确保多列选择器能够正确响应外部状态变化：

```kotlin
// 记住当前选中的索引，并监听 initialSelectedIndices 的变化
var selectedIndices by remember(initialSelectedIndices) { 
    mutableStateOf(initialSelectedIndices.toMutableList()) 
}

// 当 initialSelectedIndices 变化时，更新 selectedIndices
LaunchedEffect(initialSelectedIndices) {
    selectedIndices = initialSelectedIndices.toMutableList()
}
```

### 4. 改进初始滚动

将所有的初始滚动从 `scrollToItem` 改为 `animateScrollToItem`，提供更好的用户体验：

```kotlin
// 初始滚动到选中的项（加1是因为有顶部空白项）
LaunchedEffect(initialSelectedIndex) {
    if (initialSelectedIndex >= 0 && initialSelectedIndex < options.size) {
        listState.animateScrollToItem(initialSelectedIndex + 1)
    }
}
```

## 修复效果

### 单列选择器
- ✅ 选择后自动居中显示在两条横线中间
- ✅ 拖动停止后自动吸附到最近的选项
- ✅ 初始显示时正确居中

### 多列选择器（时间选择器）
- ✅ 每一列都能正确居中显示
- ✅ 选择时间后各列都居中对齐
- ✅ 级联选择时正确更新各列状态

### 三列选择器（地区选择器）
- ✅ 省份、城市、区县三列都能正确居中
- ✅ 级联选择时下级选项正确重置并居中
- ✅ 选择完成后所有列都居中显示

## 测试验证

创建了 `SelectorCenteringTest.kt` 测试页面，包含：
1. 简单数字选择器测试（1-100）
2. 地区选择器测试（三级联动）
3. 时间选择器测试（小时:分钟）

通过这些测试可以验证所有选择器的居中效果是否正常。

## 技术细节

### 关键实现点

1. **监听滚动状态**：使用 `listState.isScrollInProgress` 监听滚动是否停止
2. **计算中心项**：通过可见项信息计算最接近中心的项
3. **自动滚动**：使用 `animateScrollToItem` 提供平滑的滚动动画
4. **索引偏移**：考虑顶部空白项的偏移量（+1）

### 性能优化

- 使用 `LaunchedEffect` 避免不必要的重组
- 使用 `derivedStateOf` 优化中心项计算
- 使用 `remember` 缓存状态避免重复创建

## 兼容性

- ✅ 保持原有 API 不变
- ✅ 向后兼容所有现有用法
- ✅ 不影响现有功能的正常使用
