package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import kotlinx.datetime.LocalTime

/**
 * 时间选择器对话框状态管理
 */
@Stable
interface TimeSelectorDialogState {
    val visible: Boolean
    
    fun show(
        value: LocalTime? = null,
        type: TimeType = TimeType.SECOND,
        start: LocalTime = LocalTime(0, 0, 0),
        end: LocalTime = LocalTime(23, 59, 59),
        onConfirm: (LocalTime) -> Unit
    )
    
    fun hide()
}

@Composable
fun rememberTimeSelectorDialogState(): TimeSelectorDialogState {
    val state = remember { TimeSelectorDialogStateImpl() }
    
    state.props?.let { props ->
        TimeSelectorDialog(
            showDialog = state.visible,
            value = props.value,
            type = props.type,
            start = props.start,
            end = props.end,
            onDismiss = { state.hide() },
            onConfirm = { time ->
                props.onConfirm(time)
                state.hide()
            }
        )
    }
    
    return state
}

private class TimeSelectorDialogStateImpl : TimeSelectorDialogState {
    override var visible by mutableStateOf(false)
    var props by mutableStateOf<TimeSelectorProps?>(null)
        private set
    
    override fun show(
        value: LocalTime?,
        type: TimeType,
        start: LocalTime,
        end: LocalTime,
        onConfirm: (LocalTime) -> Unit
    ) {
        props = TimeSelectorProps(value, type, start, end, onConfirm)
        visible = true
    }
    
    override fun hide() {
        visible = false
    }
}

private data class TimeSelectorProps(
    val value: LocalTime?,
    val type: TimeType,
    val start: LocalTime,
    val end: LocalTime,
    val onConfirm: (LocalTime) -> Unit
)

/**
 * 地区选择器对话框状态管理
 */
@Stable
interface RegionSelectorDialogState {
    val visible: Boolean
    
    fun show(
        title: String? = null,
        values: Array<Int>,
        onConfirm: (Array<Int>) -> Unit
    )
    
    fun hide()
}

@Composable
fun rememberRegionSelectorDialogState(): RegionSelectorDialogState {
    val state = remember { RegionSelectorDialogStateImpl() }
    
    state.props?.let { props ->
        RegionSelectorDialog(
            showDialog = state.visible,
            title = props.title,
            values = props.values,
            onDismiss = { state.hide() },
            onConfirm = { values ->
                props.onConfirm(values)
                state.hide()
            }
        )
    }
    
    return state
}

private class RegionSelectorDialogStateImpl : RegionSelectorDialogState {
    override var visible by mutableStateOf(false)
    var props by mutableStateOf<RegionSelectorProps?>(null)
        private set
    
    override fun show(
        title: String?,
        values: Array<Int>,
        onConfirm: (Array<Int>) -> Unit
    ) {
        props = RegionSelectorProps(title, values, onConfirm)
        visible = true
    }
    
    override fun hide() {
        visible = false
    }
}

private data class RegionSelectorProps(
    val title: String?,
    val values: Array<Int>,
    val onConfirm: (Array<Int>) -> Unit
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as RegionSelectorProps

        if (title != other.title) return false
        if (!values.contentEquals(other.values)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = title?.hashCode() ?: 0
        result = 31 * result + values.contentHashCode()
        return result
    }
}

/**
 * 多列选择器对话框状态管理
 */
@Stable
interface MultiColumnSelectorDialogState {
    val visible: Boolean

    fun show(
        title: String,
        columns: List<List<String>>,
        initialSelectedIndices: List<Int>,
        onColumnValueChange: ((columnIndex: Int, optionIndex: Int, option: String) -> Unit)? = null,
        onConfirm: (List<Int>, List<String>) -> Unit
    )

    fun hide()
}

@Composable
fun rememberMultiColumnSelectorDialogState(): MultiColumnSelectorDialogState {
    val state = remember { MultiColumnSelectorDialogStateImpl() }

    state.props?.let { props ->
        MultiColumnSelectorDialog(
            showDialog = state.visible,
            title = props.title,
            columns = props.columns,
            initialSelectedIndices = props.initialSelectedIndices,
            onDismiss = { state.hide() },
            onColumnValueChange = props.onColumnValueChange,
            onConfirm = { indices, options ->
                props.onConfirm(indices, options)
                state.hide()
            }
        )
    }

    return state
}

private class MultiColumnSelectorDialogStateImpl : MultiColumnSelectorDialogState {
    override var visible by mutableStateOf(false)
    var props by mutableStateOf<MultiColumnSelectorProps?>(null)
        private set

    override fun show(
        title: String,
        columns: List<List<String>>,
        initialSelectedIndices: List<Int>,
        onColumnValueChange: ((columnIndex: Int, optionIndex: Int, option: String) -> Unit)?,
        onConfirm: (List<Int>, List<String>) -> Unit
    ) {
        props = MultiColumnSelectorProps(title, columns, initialSelectedIndices, onColumnValueChange, onConfirm)
        visible = true
    }

    override fun hide() {
        visible = false
    }
}

private data class MultiColumnSelectorProps(
    val title: String,
    val columns: List<List<String>>,
    val initialSelectedIndices: List<Int>,
    val onColumnValueChange: ((columnIndex: Int, optionIndex: Int, option: String) -> Unit)?,
    val onConfirm: (List<Int>, List<String>) -> Unit
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as MultiColumnSelectorProps

        if (title != other.title) return false
        if (columns != other.columns) return false
        if (initialSelectedIndices != other.initialSelectedIndices) return false

        return true
    }

    override fun hashCode(): Int {
        var result = title.hashCode()
        result = 31 * result + columns.hashCode()
        result = 31 * result + initialSelectedIndices.hashCode()
        return result
    }
}
