package com.ymx.photovoltaic.ui.page.my

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.MyViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.change_password
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_change
import photovoltaic_kmp_app.composeapp.generated.resources.confirm_new_password
import photovoltaic_kmp_app.composeapp.generated.resources.field_required
import photovoltaic_kmp_app.composeapp.generated.resources.input_confirm_new_password
import photovoltaic_kmp_app.composeapp.generated.resources.input_new_password
import photovoltaic_kmp_app.composeapp.generated.resources.input_old_password
import photovoltaic_kmp_app.composeapp.generated.resources.new_password
import photovoltaic_kmp_app.composeapp.generated.resources.old_password
import photovoltaic_kmp_app.composeapp.generated.resources.password_not_match_tip


@Composable
fun ChangePwdPage(
    navHostController: NavHostController,
    myViewModel: MyViewModel = getKoin().get()
) {
    // 获取字符串资源
    val oldPasswordText = stringResource(Res.string.old_password)
    val inputOldPasswordText = stringResource(Res.string.input_old_password)
    val newPasswordText = stringResource(Res.string.new_password)
    val inputNewPasswordText = stringResource(Res.string.input_new_password)
    val confirmNewPasswordText = stringResource(Res.string.confirm_new_password)
    val inputConfirmNewPasswordText = stringResource(Res.string.input_confirm_new_password)
    val fieldRequiredText = stringResource(Res.string.field_required)
    val passwordNotMatchTipText = stringResource(Res.string.password_not_match_tip)
    val confirmChangeText = stringResource(Res.string.confirm_change)
    val changePasswordText = stringResource(Res.string.change_password)

    // 状态变量
    var oldPassword by remember { mutableStateOf("") }
    var oldPasswordVisibility by remember { mutableStateOf(false) }
    var newPassword by remember { mutableStateOf("") }
    var newPasswordVisibility by remember { mutableStateOf(false) }
    var newPasswordAgain by remember { mutableStateOf("") }
    var newPasswordVisibilityAgain by remember { mutableStateOf(false) }
    var showFailDialog by remember { mutableStateOf(false) }
    var showSuccessDialog by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    // 错误状态
    var oldPasswordError by remember { mutableStateOf(false) }
    var newPasswordError by remember { mutableStateOf(false) }
    var newPasswordAgainError by remember { mutableStateOf(false) }
    var passwordMismatchError by remember { mutableStateOf(false) } // 用于密码不匹配错误

    fun performChangePassword() {
        // 重置错误状态
        oldPasswordError = false
        newPasswordError = false
        newPasswordAgainError = false
        passwordMismatchError = false

        var hasError = false

        if (oldPassword.isEmpty()) {
            oldPasswordError = true
            hasError = true
        }
        if (newPassword.isEmpty()) {
            newPasswordError = true
            hasError = true
        }
        if (newPasswordAgain.isEmpty()) {
            newPasswordAgainError = true
            hasError = true
        }

        // 检查密码是否一致 (仅在两个密码字段都已填写且无空值错误时检查)
        if (!hasError && newPassword != newPasswordAgain) {
            passwordMismatchError = true
            hasError = true
        }

        if (hasError) {
            return
        }

        // 调用ViewModel
        myViewModel.changePwd(
            AppGlobal.userName,
            oldPassword,
            newPassword,
            errorBlock = {
                showFailDialog = true
                coroutineScope.launch {
                    delay(2000)
                    showFailDialog = false
                }
            }
        ) {
            showSuccessDialog = true
            coroutineScope.launch {
                delay(2000)
                showSuccessDialog = false
                // 修改密码成功后通常返回上一页或设置页，而不是登录页
                navHostController.popBackStack()
            }
        }
    }

    SetStatusBar(Color.White,true)

    Scaffold(
        topBar = {
            TopBar(changePasswordText, backClick = { navHostController.popBackStack() })
        },
        backgroundColor = Grey_F5
    ) {
        paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues).padding(start = 10.dp, end = 10.dp, top = 20.dp, bottom = 30.dp)
        ) {
            // 旧密码输入框
            CommonTitleField(
                titleText = oldPasswordText,
                value = oldPassword,
                onValueChange = {
                    oldPassword = it
                    oldPasswordError = false
                },
                placeholderCom = { Text(inputOldPasswordText) },
                passwordVisibility = oldPasswordVisibility,
                onPasswordVisibilityChange = { oldPasswordVisibility = !oldPasswordVisibility },
                modifier = Modifier.fillMaxWidth(),
                isError = oldPasswordError,
                errorText = if (oldPasswordError) fieldRequiredText else "",
                isPassword = true
            )

            Spacer(modifier = Modifier.height(12.dp)) // 增加间距

            // 新密码输入框
            CommonTitleField(
                titleText = newPasswordText,
                value = newPassword,
                onValueChange = {
                    newPassword = it
                    newPasswordError = false
                    // 当新密码或确认密码改变时，检查不匹配错误
                    passwordMismatchError = newPassword != newPasswordAgain && newPasswordAgain.isNotEmpty()
                },
                placeholderCom = { Text(inputNewPasswordText) },
                passwordVisibility = newPasswordVisibility,
                onPasswordVisibilityChange = { newPasswordVisibility = !newPasswordVisibility },
                modifier = Modifier.fillMaxWidth(),
                isError = newPasswordError || passwordMismatchError, // 同时检查空值和不匹配错误
                errorText = when {
                    newPasswordError -> fieldRequiredText
                    passwordMismatchError -> passwordNotMatchTipText // 不匹配提示优先
                    else -> ""
                },
                isPassword = true
            )

            Spacer(modifier = Modifier.height(12.dp)) // 增加间距

            // 确认新密码输入框
            CommonTitleField(
                titleText = confirmNewPasswordText,
                value = newPasswordAgain,
                onValueChange = {
                    newPasswordAgain = it
                    newPasswordAgainError = false
                    // 当新密码或确认密码改变时，检查不匹配错误
                    passwordMismatchError = newPassword != newPasswordAgain && newPassword.isNotEmpty()
                },
                placeholderCom = { Text(inputConfirmNewPasswordText) },
                passwordVisibility = newPasswordVisibilityAgain,
                onPasswordVisibilityChange = { newPasswordVisibilityAgain = !newPasswordVisibilityAgain },
                modifier = Modifier.fillMaxWidth(),
                isError = newPasswordAgainError || passwordMismatchError, // 同时检查空值和不匹配错误
                errorText = when {
                    newPasswordAgainError -> fieldRequiredText
                    passwordMismatchError -> passwordNotMatchTipText // 不匹配提示优先
                    else -> ""
                },
                isPassword = true
            )

            Spacer(modifier = Modifier.weight(1f))

            // 确认按钮 (始终启用，点击时校验)
            ConfirmButton(confirmChangeText, true) {
                performChangePassword()
            }

            Spacer(modifier = Modifier.height(30.dp)) // 底部增加间距

            if (showFailDialog) {
                ResultDialog(false) { showFailDialog = false }
            }

            if (showSuccessDialog) {
                ResultDialog(true) { showSuccessDialog = false }
            }
        }
    }
}




