package com.ymx.photovoltaic.ui.page.user

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.DropdownMenu
import androidx.compose.material.DropdownMenuItem
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.config.AppConfig
import com.ymx.photovoltaic.data.local.CacheManager
import com.ymx.photovoltaic.language.Language
import com.ymx.photovoltaic.navigation.Route
import com.ymx.photovoltaic.platform.SetStatusBar
import com.ymx.photovoltaic.platform.updateLanguage
import com.ymx.photovoltaic.ui.page.theme.BlueColor
import com.ymx.photovoltaic.ui.page.theme.LjRed
import com.ymx.photovoltaic.ui.page.widget.CommonTitleField
import com.ymx.photovoltaic.ui.page.widget.GradientButton
import com.ymx.photovoltaic.ui.page.widget.LoadingDialog
import com.ymx.photovoltaic.ui.page.widget.PlaceholderText
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.util.encodeUrl
import com.ymx.photovoltaic.viewmodel.LanguageViewModel
import com.ymx.photovoltaic.viewmodel.UserViewModel
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.and
import photovoltaic_kmp_app.composeapp.generated.resources.forgot_password
import photovoltaic_kmp_app.composeapp.generated.resources.i_have_read
import photovoltaic_kmp_app.composeapp.generated.resources.input_password
import photovoltaic_kmp_app.composeapp.generated.resources.input_user_name
import photovoltaic_kmp_app.composeapp.generated.resources.language_switch
import photovoltaic_kmp_app.composeapp.generated.resources.lj_x10
import photovoltaic_kmp_app.composeapp.generated.resources.logging_in
import photovoltaic_kmp_app.composeapp.generated.resources.login
import photovoltaic_kmp_app.composeapp.generated.resources.login_back
import photovoltaic_kmp_app.composeapp.generated.resources.login_failed
import photovoltaic_kmp_app.composeapp.generated.resources.no_account
import photovoltaic_kmp_app.composeapp.generated.resources.privacy_policy
import photovoltaic_kmp_app.composeapp.generated.resources.remember_credentials
import photovoltaic_kmp_app.composeapp.generated.resources.to_register
import photovoltaic_kmp_app.composeapp.generated.resources.user_agreement
import photovoltaic_kmp_app.composeapp.generated.resources.welcome_hello
import photovoltaic_kmp_app.composeapp.generated.resources.welcome_login

// 自定义绘制小尺寸RadioButton组件
@Composable
fun MiniRadioButton(
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    selectedColor: Color = Color.White,
    unselectedColor: Color = Color.White
) {
    Box(
        modifier = modifier
            .size(12.dp) // 指定固定大小
            .clickable(enabled = enabled, onClick = onClick)
    ) {
        Canvas(modifier = Modifier.matchParentSize()) {
            // 绘制外圆
            drawCircle(
                color = if (enabled) unselectedColor else unselectedColor.copy(alpha = 0.6f),
                radius = size.minDimension / 2,
                style = Stroke(width = size.minDimension / 10)
            )
            
            // 如果选中，绘制内圆
            if (selected) {
                drawCircle(
                    color = if (enabled) selectedColor else selectedColor.copy(alpha = 0.6f),
                    radius = size.minDimension / 4
                )
            }
        }
    }
}

@Composable
fun LoginPage(
    navHostController: NavHostController,
    userViewModel: UserViewModel = getKoin().get(),
    languageViewModel: LanguageViewModel = getKoin().get()
) {

    // 设置状态栏和导航栏颜色
    SetStatusBar(Color.Black, false)

    val keyboardController = LocalSoftwareKeyboardController.current

    var username by rememberSaveable { mutableStateOf(CacheManager.getSavedUsername()) }
    var password by rememberSaveable { mutableStateOf(CacheManager.getSavedPassword()) }
    var passwordVisibility by remember { mutableStateOf(false) }
    var showLoadingDialog by remember { mutableStateOf(false) }
    var isAgreementChecked by remember { mutableStateOf(true) }
    var isRememberCredentials by remember { mutableStateOf(CacheManager.isRememberCredentials()) }
    
    // 监听语言变化，触发整个页面重组
    val languageState = languageViewModel.language.collectAsState()
    LaunchedEffect(languageState.value) {
        // 空实现，但会在语言变化时触发整个LoginPage重组
    }

    if (showLoadingDialog) {
        LoadingDialog(loadingText = stringResource(Res.string.logging_in)) { showLoadingDialog = false }
    }

    var showFailDialog by remember { mutableStateOf(false) }

    if (showFailDialog) {
        ResultDialog(false, stringResource(Res.string.login_failed)) { showFailDialog = false }
    }

    val registrationId=""

    // 定义语言选择状态
    var expanded by remember { mutableStateOf(false) }
    
    // 获取语言选项和当前语言
    val languages = listOf(
        Language("zh", "中文"),
        Language("en", "English"),
    )
    
    // 使用collectAsState订阅ViewModel中的language StateFlow
    val currentLanguage = when(languageState.value.code) {
        "en" -> languages[1]
        else -> languages[0]
    }



    Scaffold()
    {
        // 使用Box作为根容器，以便背景图片可以延伸到整个屏幕
        Box(modifier = Modifier.fillMaxSize().background(Color.Black)) {
            // 背景图片
            Image(
                painter = painterResource(Res.drawable.login_back),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )

            // 内容区域
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxSize()
            ) {
                // 顶部语言切换图标
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 40.dp, start = 20.dp, end = 20.dp),
                    horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box {
                        Image(
                            painter = painterResource(Res.drawable.language_switch),
                            contentDescription = null,
                            modifier = Modifier
                                .size(30.dp)
                                .clickable {
                                    // 点击时显示语言选择菜单
                                    expanded = !expanded
                                }
                        )

                        // 语言选择器
                        DropdownMenu(
                            expanded = expanded,
                            onDismissRequest = { expanded = false },
                            modifier = Modifier.background(Color.White)
                        ) {
                            languages.forEachIndexed { index, language ->
                                DropdownMenuItem(
                                    onClick = {
                                        // 设置新语言，ViewModel会通过StateFlow通知UI更新
                                        languageViewModel.setLanguage(language)
                                        // 更新系统语言设置
                                        updateLanguage()
                                        expanded = false
                                    }
                                ) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = language.name,
                                            fontSize = 16.sp,
                                            style = TextStyle(fontWeight = FontWeight.Normal)
                                        )
                                        if (language.code == currentLanguage.code) {
                                            Icon(
                                                imageVector = Icons.Default.Check,
                                                contentDescription = "Selected",
                                                tint = Color.Blue
                                            )
                                        }
                                    }
                                }

                                if (index < languages.size - 1) {
                                    HorizontalDivider(thickness = 0.5.dp, color = Color.LightGray)
                                }
                            }
                        }
                    }
                }

                // LJ-X10图标
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 0.dp, start = 45.dp),
                    horizontalArrangement = Arrangement.Start
                ) {
                    Image(
                        painter = painterResource(Res.drawable.lj_x10),
                        contentDescription = null,
                        modifier = Modifier.width(200.dp).height(180.dp)
                    )
                }

                // 添加一个占位Spacer，将内容移到屏幕3/5位置
                Spacer(modifier = Modifier.weight(0.42f)) // 使用屏幕高度的40%

                // 欢迎登录文本
                Row(
                    modifier = Modifier
                        .padding(start = 45.dp)
                        .align(Alignment.Start)
                ) {
                    // Hello! 文本 - 使用LjRed颜色
                    Text(
                        text = stringResource(Res.string.welcome_hello),
                        style = TextStyle(
                            color = LjRed,
                            fontWeight = FontWeight.Bold,
                            fontSize = 18.sp
                        )
                    )

                    // 欢迎登录文本 - 使用白色
                    Text(
                        text =" "+ stringResource(Res.string.welcome_login),
                        style = TextStyle(
                            color = Color.White,
                            fontWeight = FontWeight.Bold,
                            fontSize = 18.sp
                        )
                    )
                }

                Spacer(modifier = Modifier.height(10.dp))

                // 用户名输入框
                CommonTitleField(
                    value = username,
                    onValueChange = { username = it },
                    placeholderCom = { PlaceholderText(Res.string.input_user_name) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 30.dp),
                    cornerRadius = 25,
                    textFieldHeight = 50,
                    backgroundColor = Color.White,
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    onBoxClick = { keyboardController?.hide() }
                )

                Spacer(modifier = Modifier.height(20.dp))

                // 密码输入框
                CommonTitleField(
                    value = password,
                    onValueChange = { password = it },
                    placeholderCom = { PlaceholderText(Res.string.input_password) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 30.dp),
                    cornerRadius = 25,
                    textFieldHeight = 50,
                    backgroundColor = Color.White,
                    isPassword = true,
                    passwordVisibility = passwordVisibility,
                    onPasswordVisibilityChange = { passwordVisibility = !passwordVisibility },
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    onBoxClick = { keyboardController?.hide() }
                )

                // 记住密码复选框
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 45.dp,end=50.dp, top = 15.dp, bottom = 5.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                    MiniRadioButton(
                        selected = isRememberCredentials,
                        onClick = {
                            isRememberCredentials = !isRememberCredentials
                            CacheManager.saveRememberCredentials(isRememberCredentials)
                        }
                    )
                    Text(
                        text = stringResource(Res.string.remember_credentials),
                        fontSize = 13.sp,
                        color = Color.White,
                        modifier = Modifier
                            .clickable {
                                isRememberCredentials = !isRememberCredentials
                                CacheManager.saveRememberCredentials(isRememberCredentials)
                            }
                            .padding(start = 5.dp) // 调整文本与单选框距离
                    )
                }


                    // 忘记密码 - 右侧
                    Text(
                        text = stringResource(Res.string.forgot_password).replace("%s", "?"),
                        fontSize = 13.sp,
                        color = LjRed,
                        modifier = Modifier
                            .padding(end = 20.dp)
                            .clickable {
                                navHostController.navigate(Route.FORGOT_PASSWORD)
                            }
                    )
                }




                // 用户协议行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 45.dp, vertical = 5.dp),
                    horizontalArrangement = Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    MiniRadioButton(
                        selected = isAgreementChecked,
                        onClick = { isAgreementChecked = !isAgreementChecked }
                    )

                    Text(
                        text = stringResource(Res.string.i_have_read),
                        fontSize = 13.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(start = 5.dp, end = 2.dp) // 调整文本与单选框距离
                    )

                    val language = CacheManager.getLanguage()
                    val userAgreement = if (language == "zh") {
                        stringResource(Res.string.user_agreement).replace("%s", "")  // 中文不添加空格
                    } else {
                        stringResource(Res.string.user_agreement).replace("%s", " ")  // 其他语言添加空格
                    }

                    // 根据当前语言获取对应的协议URL
                    val userAgreementUrl = AppConfig.getUserAgreementUrl()

                    Text(
                        text = userAgreement,
                        fontSize = 13.sp,
                        color = BlueColor,
                        modifier = Modifier.clickable {
                            val encodedUrl = encodeUrl(userAgreementUrl)
                            val encodedTitle = encodeUrl(userAgreement)
                            navHostController.navigate("web_view?title=$encodedTitle&url=$encodedUrl")
                        }
                    )
                    val and = if (language == "zh") {
                        stringResource(Res.string.and).replace("%s", "")  // 中文不添加空格
                    } else {
                        stringResource(Res.string.and).replace("%s", " ")  // 其他语言添加空格
                    }
                    Text(
                        text = and,
                        fontSize = 13.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(horizontal = 2.dp)
                    )

                    val privacyPolicy = if (language == "zh") {
                        stringResource(Res.string.privacy_policy).replace("%s", "")  // 中文不添加空格
                    } else {
                        stringResource(Res.string.privacy_policy).replace("%s", " ")  // 其他语言添加空格
                    }

                    // 根据当前语言获取对应的隐私政策URL
                    val privacyPolicyUrl = AppConfig.getPrivacyPolicyUrl()

                    Text(
                        text = privacyPolicy,
                        fontSize = 13.sp,
                        color = BlueColor,
                        modifier = Modifier.clickable {
                            val encodedUrl = encodeUrl(privacyPolicyUrl)
                            val encodedTitle = encodeUrl(privacyPolicy)
                            navHostController.navigate("web_view?title=$encodedTitle&url=$encodedUrl")
                        }
                    )
                }

                // 登录按钮
                GradientButton(
                    text = stringResource(Res.string.login),
                    onClick = {
                        // 执行登录操作
                        showLoadingDialog = true
                        showFailDialog = false
                        userViewModel.login(
                            username,
                            password,
                            registrationId,
                            errorBlock = {
                                showLoadingDialog = false
                                showFailDialog = true
                            },
                        ) {
                            // 登录成功后的操作
                            showLoadingDialog = false

                            // 如果选择记住密码，保存用户名和密码
                            if (isRememberCredentials) {
                                CacheManager.saveUsername(username)
                                CacheManager.savePassword(password)
                            } else {
                                CacheManager.clearSavedCredentials()
                            }

                            navHostController.navigate(Route.HOME)
                            {
                                popUpTo(Route.LOGIN) { inclusive = true }
                            }
                        }
                    },
                    enabled = username.trim().isNotEmpty() && password.trim().isNotEmpty() && isAgreementChecked,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 30.dp)
                        .padding(top = 10.dp),
                    languageState = languageState.value
                )

                // 底部文本行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 45.dp)
                        .padding(top = 20.dp, bottom = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = stringResource(Res.string.no_account),
                            color = Color.Gray,
                            fontSize = 14.sp
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = stringResource(Res.string.to_register),
                            modifier = Modifier.clickable {
                                navHostController.navigate(Route.ACCOUNT_TYPE)
                            },
                            color = LjRed,
                            fontSize = 14.sp
                        )
                    }

                }

                Spacer(modifier = Modifier.weight(0.05f))


            }
        }
    }

}


