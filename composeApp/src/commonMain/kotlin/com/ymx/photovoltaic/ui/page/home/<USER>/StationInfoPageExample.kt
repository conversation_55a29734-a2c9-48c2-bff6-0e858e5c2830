package com.ymx.photovoltaic.ui.page.home.edit

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.data.AppGlobal
import com.ymx.photovoltaic.platform.ToastUtil
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.ConfirmButton
import com.ymx.photovoltaic.ui.page.widget.CustomInput
import com.ymx.photovoltaic.ui.page.widget.RegionDataManager
import com.ymx.photovoltaic.ui.page.widget.ResultDialog
import com.ymx.photovoltaic.ui.page.widget.SelectorDialog
import com.ymx.photovoltaic.ui.page.widget.TimeType
import com.ymx.photovoltaic.ui.page.widget.TimeSelectorDialog
import com.ymx.photovoltaic.ui.page.widget.RegionSelectorDialog
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.ui.page.widget.rememberTimeSelectorDialogState
import com.ymx.photovoltaic.ui.page.widget.rememberRegionSelectorDialogState
import com.ymx.photovoltaic.viewmodel.EditViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.datetime.LocalTime
import org.jetbrains.compose.resources.stringResource
import org.koin.mp.KoinPlatform.getKoin
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.contact_way
import photovoltaic_kmp_app.composeapp.generated.resources.power
import photovoltaic_kmp_app.composeapp.generated.resources.region
import photovoltaic_kmp_app.composeapp.generated.resources.save
import photovoltaic_kmp_app.composeapp.generated.resources.select_station_type
import photovoltaic_kmp_app.composeapp.generated.resources.selected_region
import photovoltaic_kmp_app.composeapp.generated.resources.station_info
import photovoltaic_kmp_app.composeapp.generated.resources.street_name
import photovoltaic_kmp_app.composeapp.generated.resources.sunDowntime
import photovoltaic_kmp_app.composeapp.generated.resources.sunUptime
import photovoltaic_kmp_app.composeapp.generated.resources.system_name
import photovoltaic_kmp_app.composeapp.generated.resources.time_error
import photovoltaic_kmp_app.composeapp.generated.resources.type_2_4g
import photovoltaic_kmp_app.composeapp.generated.resources.type_plc
import photovoltaic_kmp_app.composeapp.generated.resources.unit_kw

/**
 * 使用新的 SelectorDialog 组件的 StationInfoPage 示例
 * 展示如何替换原有的 rememberCustomTimePickerState 和 rememberRegionPickerState
 */
@Composable
fun StationInfoPageExample(
    navHostController: NavHostController,
    editViewModel: EditViewModel = getKoin().get()
) {
    val station by editViewModel.stationFlow.collectAsState()

    // 使用新的选择器状态管理
    val startTimePicker = rememberTimeSelectorDialogState()
    val endTimePicker = rememberTimeSelectorDialogState()
    val regionPicker = rememberRegionSelectorDialogState()
    
    // 获取字符串资源
    val type2_4g = stringResource(Res.string.type_2_4g)
    val typePlc = stringResource(Res.string.type_plc)
    val selectedRegionTitle = stringResource(Res.string.selected_region)
    val selectStationType = stringResource(Res.string.select_station_type)
    val timeErrorStr = stringResource(Res.string.time_error)
    
    var regionValues by remember { mutableStateOf(arrayOf(0, 0, 0)) }
    var selectedRegionText by remember { mutableStateOf("") }
    var typeValue by remember { mutableStateOf(0) }
    var typeText by remember { mutableStateOf("") }
    
    // 类型选择器状态
    var showTypeSelector by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        editViewModel.fetchStation(AppGlobal.powerStationId)
    }

    var systemNameValue by remember { mutableStateOf("") }
    var streetNameValue by remember { mutableStateOf("") }
    var powerValue by remember { mutableStateOf("") }
    var sunuptimeValue by remember { mutableStateOf("") }
    var sundowntimeValue by remember { mutableStateOf("") }

    var countriesId by remember { mutableStateOf("") }
    var province by remember { mutableStateOf("") }
    var cityId by remember { mutableStateOf("") }
    var districtId by remember { mutableStateOf("") }

    // 更新初始值
    LaunchedEffect(station) {
        station?.let {
            systemNameValue = it.systemName
            selectedRegionText = "${it.countries} ${it.provinceName} ${it.cityName}"
            streetNameValue = it.streetName
            powerValue = it.power.toString()
            sunuptimeValue = it.sunuptime
            sundowntimeValue = it.sundowntime
            typeText = if (it.type == 1) type2_4g else typePlc
            typeValue = if(it.type>=1) it.type.minus(1) else 0
            countriesId = it.countriesId
            province = it.province
            cityId = it.cityId
            districtId=it.districtId
        }
    }

    Scaffold(
        topBar = {
            TopBar(stringResource(Res.string.station_info), backClick = { navHostController.popBackStack() })
        },
        containerColor = Grey_F5
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(start = 10.dp, end = 10.dp, top = 15.dp, bottom = 60.dp)
        ) {
            var showSuccessDialog by remember { mutableStateOf(false) }
            var showFailDialog by remember { mutableStateOf(false) }

            if (showSuccessDialog) {
                ResultDialog(true) { showSuccessDialog = false }
            }
            if (showFailDialog) {
                ResultDialog(false) { showFailDialog = false }
            }

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, shape = RoundedCornerShape(8.dp))
                    .padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 20.dp)
            ) {
                CustomInput(
                    value = systemNameValue,
                    label = stringResource(Res.string.system_name),
                    placeholder = "",
                    onChange = { systemNameValue = it },
                    textAlign = TextAlign.Right
                )

                CustomInput(
                    value = selectedRegionText,
                    label = stringResource(Res.string.region),
                    placeholder = "",
                    onChange = { },
                    onClick = {
                        regionPicker.show(
                            title = selectedRegionTitle,
                            values = regionValues
                        ) { selected ->
                            regionValues = selected
                            // 获取选中的地区对象
                            val region1 = RegionDataManager.getRegionsByLevel(2).getOrNull(selected[0])
                            val region2Children = RegionDataManager.getChildren(region1?.id ?: 0)
                            val region2 = if (region2Children.isEmpty()) null else region2Children.getOrNull(selected[1])
                            
                            val region3Children = RegionDataManager.getChildren(region2?.id ?: 0)
                            val region3 = if (region3Children.isEmpty()) null else region3Children.getOrNull(selected[2])
                            
                            selectedRegionText = "${region1?.name ?: ""} ${region2?.name ?: ""} ${region3?.name ?: ""}"
                            countriesId = region1?.id?.toString() ?: ""
                            province = region2?.id?.toString() ?: ""
                            cityId = region3?.id?.toString() ?: ""
                            
                            // 处理直辖市的特殊情况
                            val directCities = listOf("北京", "上海", "天津", "重庆")
                            districtId = if (region2?.name in directCities) {
                                region2?.name?:""
                            } else {
                                region3?.name ?: ""
                            }
                        }
                    },
                    textAlign = TextAlign.Right
                )

                CustomInput(
                    value = streetNameValue,
                    label = stringResource(Res.string.street_name),
                    placeholder = "",
                    onChange = { streetNameValue = it },
                    textAlign = TextAlign.Right
                )

                CustomInput(
                    value = powerValue,
                    label = stringResource(Res.string.power) + "(" + stringResource(Res.string.unit_kw) + ")",
                    placeholder = "",
                    labelWidth = 120.dp,
                    onChange = { newValue ->
                        val filteredValue = newValue.filter { it.isDigit() }
                        powerValue = filteredValue
                    },
                    textAlign = TextAlign.Right
                )

                CustomInput(
                    value = typeText,
                    label = stringResource(Res.string.contact_way),
                    placeholder = "",
                    onChange = { },
                    onClick = {
                        showTypeSelector = true
                    },
                    textAlign = TextAlign.Right
                )

                CustomInput(
                    value = sunuptimeValue,
                    label = stringResource(Res.string.sunUptime),
                    placeholder = "",
                    labelWidth = 130.dp,
                    onChange = { sunuptimeValue = it },
                    onClick = {
                        startTimePicker.show(
                            value = LocalTime.parse(sunuptimeValue), 
                            type = TimeType.MINUTE
                        ) {
                            sunuptimeValue = it.toString()
                        }
                    },
                    textAlign = TextAlign.Right
                )

                CustomInput(
                    value = sundowntimeValue,
                    label = stringResource(Res.string.sunDowntime),
                    placeholder = "",
                    labelWidth = 130.dp,
                    onChange = { sundowntimeValue = it },
                    onClick = {
                        endTimePicker.show(
                            value = LocalTime.parse(sundowntimeValue), 
                            type = TimeType.MINUTE
                        ) { selectedTime ->
                            val startTime = LocalTime.parse(sunuptimeValue)
                            if (selectedTime < startTime) {
                                ToastUtil.showShort(timeErrorStr)
                                return@show
                            }
                            sundowntimeValue = selectedTime.toString()
                        }
                    },
                    textAlign = TextAlign.Right
                )
            }

            // 类型选择器对话框
            SelectorDialog(
                showDialog = showTypeSelector,
                title = selectStationType,
                options = listOf(type2_4g, typePlc),
                initialSelectedIndex = typeValue,
                onDismiss = { showTypeSelector = false },
                onOptionSelected = { index ->
                    typeValue = index
                },
                onConfirm = { index, option ->
                    typeValue = index
                    typeText = option
                    showTypeSelector = false
                }
            )

            Spacer(modifier = Modifier.weight(1f))

            val isEnabled = systemNameValue.trim().isNotEmpty() &&
                    selectedRegionText.trim().isNotEmpty() &&
                    streetNameValue.trim().isNotEmpty() &&
                    powerValue.trim().isNotEmpty() &&
                    typeText.trim().isNotEmpty() &&
                    sunuptimeValue.trim().isNotEmpty() &&
                    sundowntimeValue.trim().isNotEmpty()&&
                    countriesId.trim().isNotEmpty()

            val coroutineScope = rememberCoroutineScope()
            ConfirmButton(stringResource(Res.string.save), isEnabled) {
                val stationMap = mapOf(
                    "id" to AppGlobal.powerStationId,
                    "memberId" to AppGlobal.mId,
                    "systemName" to systemNameValue.trim(),
                    "countriesId" to countriesId.trim(),
                    "province" to province.trim(),
                    "cityId" to cityId.trim(),
                    "districtId" to districtId.trim(),
                    "streetName" to streetNameValue.trim(),
                    "power" to powerValue.trim(),
                    "type" to (typeValue + 1).toString(),
                    "sunuptime" to sunuptimeValue.trim(),
                    "sundowntime" to sundowntimeValue.trim()
                )

                editViewModel.updatePowerStationModel(stationMap,
                    errorBlock = {
                        showFailDialog = true
                        coroutineScope.launch {
                            delay(2000)
                            showFailDialog = false
                        }
                    }
                ) {
                    showSuccessDialog = true
                    coroutineScope.launch {
                        delay(2000)
                        showSuccessDialog = false
                        navHostController.popBackStack()
                    }
                }
            }
        }
    }
}
