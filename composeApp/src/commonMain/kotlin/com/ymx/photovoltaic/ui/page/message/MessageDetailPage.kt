package com.ymx.photovoltaic.ui.page.message

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Text
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavHostController
import com.ymx.photovoltaic.ui.page.theme.Grey_F5
import com.ymx.photovoltaic.ui.page.widget.TopBar
import com.ymx.photovoltaic.viewmodel.HomeViewModel
import org.koin.mp.KoinPlatform.getKoin
import androidx.compose.runtime.LaunchedEffect
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import photovoltaic_kmp_app.composeapp.generated.resources.Res
import photovoltaic_kmp_app.composeapp.generated.resources.auto_open
import photovoltaic_kmp_app.composeapp.generated.resources.blue_warning
import photovoltaic_kmp_app.composeapp.generated.resources.message_alarm_unread
import photovoltaic_kmp_app.composeapp.generated.resources.message_detail
import photovoltaic_kmp_app.composeapp.generated.resources.message_resume_unread
import photovoltaic_kmp_app.composeapp.generated.resources.power_abnormal
import photovoltaic_kmp_app.composeapp.generated.resources.power_restored
import photovoltaic_kmp_app.composeapp.generated.resources.red_warning
import photovoltaic_kmp_app.composeapp.generated.resources.temp_shutdown
import photovoltaic_kmp_app.composeapp.generated.resources.temp_warning
import photovoltaic_kmp_app.composeapp.generated.resources.temp_warning_resolved
import photovoltaic_kmp_app.composeapp.generated.resources.warning

@Composable
fun MessageDetailPage(
    navHostController: NavHostController,
    id: String,
    type: String,
    content: String,
    time: String,
    isRead: String,
    homeViewModel: HomeViewModel = getKoin().get()
) {
    val typeInt = remember { type.toIntOrNull() ?: 1 }
    val timeValue = remember { time.toLongOrNull() ?: 0L }
    val isReadBool = remember { isRead == "true" }
    
    // 如果消息未读，则调用设置已读接口
    LaunchedEffect(id) {
        if (!isReadBool && id.isNotEmpty()) {
            homeViewModel.setMessageRead(id)
        }
    }
    
    Scaffold(
        topBar = {
            TopBar(
                textStr = stringResource(Res.string.message_detail),
                backClick = { navHostController.popBackStack() }
            )
        },
      containerColor = Grey_F5
    ) { paddingValues ->
        Column(modifier = Modifier.padding(paddingValues).padding(top = 16.dp, start = 10.dp, end = 10.dp)) {
            AlertCardDetail(
                type = typeInt,
                content = content,
                time = timeValue,
                isRead = isReadBool
            )
        }
    }
}

@Composable
fun AlertCardDetail(type: Int, content: String, time: Long, isRead: Boolean) {
    Card(
        shape = RoundedCornerShape(12.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Image(
                    painter = when(type)
                    {
                        1,2,6 -> if(isRead)
                            painterResource(Res.drawable.red_warning)
                        else painterResource(Res.drawable.message_alarm_unread)
                        4,5,7 -> if(isRead) painterResource(Res.drawable.blue_warning)
                        else painterResource(Res.drawable.message_resume_unread)
                        else -> painterResource(Res.drawable.red_warning)
                    },
                    contentDescription = "warn",
                    modifier = if(isRead) Modifier.size(26.dp) else Modifier.size(30.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = when (type)
                {
                    1 -> stringResource(Res.string.temp_warning)
                    2 -> stringResource(Res.string.power_abnormal)
                    4 -> stringResource(Res.string.temp_warning_resolved)
                    5 -> stringResource(Res.string.power_restored)
                    6 -> stringResource(Res.string.temp_shutdown)
                    7 -> stringResource(Res.string.auto_open)
                    else -> stringResource(Res.string.warning)
                },

                    fontSize = 18.sp, color = Color.Black)
                Spacer(modifier = Modifier.weight(1f))
//               Text(
//                   text = if (isRead) "已读" else "未读",
//                   fontSize = 14.sp,
//                   color = if (isRead) Color.Green else Color.Gray
//               )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Text(text = content, fontSize = 14.sp, color = Color.Gray)
            // 分割线
            HorizontalDivider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp, bottom = 10.dp),
                thickness = 1.dp,
                color = Color.LightGray
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                TimeDisplay(time)
            }
        }
    }
}
