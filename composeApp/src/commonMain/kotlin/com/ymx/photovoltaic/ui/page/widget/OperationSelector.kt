package com.ymx.photovoltaic.ui.page.widget

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.ymx.photovoltaic.ui.page.theme.LjRed

/**
 * 操作选择器组件
 *
 * @param title 标题文本，默认为"操作类型"
 * @param options 选项列表
 * @param initialSelectedIndex 初始选中的选项索引
 * @param onOptionSelected 选项被选中时的回调
 * @param onCancelClick 取消按钮点击事件
 * @param onConfirmClick 确定按钮点击事件
 */
@Composable
fun OperationSelector(
    modifier: Modifier = Modifier,
    title: String = "操作类型",
    options: List<String>,
    initialSelectedIndex: Int = 0,
    onOptionSelected: (Int, String) -> Unit = { _, _ -> },
    onCancelClick: () -> Unit = {},
    onConfirmClick: (Int, String) -> Unit = { _, _ -> }
) {
    // 记住当前选中的索引
    var selectedIndex by remember { mutableStateOf(initialSelectedIndex) }
    
    // 记住列表的滚动状态
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    
    // 初始滚动到选中的项
    LaunchedEffect(initialSelectedIndex) {
        if (initialSelectedIndex > 0 && initialSelectedIndex < options.size) {
            listState.scrollToItem(initialSelectedIndex)
        }
    }
    
    // 计算当前中心项
    val centerItemIndex by remember {
        derivedStateOf {
            val visibleItems = listState.layoutInfo.visibleItemsInfo
            if (visibleItems.isEmpty()) {
                0
            } else {
                // 中心位置是可见区域的中点
                val centerPosition = listState.layoutInfo.viewportEndOffset / 2f + listState.layoutInfo.viewportStartOffset / 2f
                
                // 查找最接近中心的项
                val centerItem = visibleItems.minByOrNull { 
                    kotlin.math.abs((it.offset + it.size / 2f) - centerPosition)
                }
                
                centerItem?.index?.minus(1) ?: 0 // -1 是因为顶部有一个空白项
            }
        }
    }
    
    // 当中心项改变时更新选中项
    LaunchedEffect(centerItemIndex) {
        if (centerItemIndex >= 0 && centerItemIndex < options.size) {
            selectedIndex = centerItemIndex
            onOptionSelected(centerItemIndex, options[centerItemIndex])
        }
    }
    
    Card(
        modifier = modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 标题
            Text(
                text = title,
                style = TextStyle(fontSize = 18.sp, fontWeight = FontWeight.Bold),
                modifier = Modifier.padding(top=30.dp,bottom = 5.dp)
            )
            
            // 选项列表容器
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(180.dp).padding(16.dp)
            ) {
                // 两条分隔线
                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .align(Alignment.TopCenter)
                        .padding(top = 40.dp),
                    thickness = 1.dp,
                    color = Color.LightGray
                )

                HorizontalDivider(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .align(Alignment.TopCenter)
                        .padding(top = 100.dp),
                    thickness = 1.dp,
                    color = Color.LightGray
                )
                
                // 选项列表
                LazyColumn(
                    state = listState,
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxSize()
                ) {
                    // 添加顶部空白项以确保第一个选项可以滚动到中间位置
                    item { Spacer(modifier = Modifier.height(40.dp)) }
                    
                    // 显示选项
                    items(options) { option ->
                        val index = options.indexOf(option)
                        val isSelected = index == selectedIndex
                        
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(60.dp)
                                .padding(horizontal = 16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = option,
                                style = TextStyle(
                                    fontSize = if (isSelected) 17.sp else 15.sp,
                                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                                    color = if (isSelected) Color.Black else Color.Gray
                                ),
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                    
                    // 添加底部空白项以确保最后一个选项可以滚动到中间位置
                    item { Spacer(modifier = Modifier.height(40.dp)) }
                }
            }


            HorizontalDivider(thickness = 0.5.dp, color = Color.LightGray)

            // 底部按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(IntrinsicSize.Min),
                verticalAlignment = Alignment.CenterVertically
            ) {

                
                // 取消按钮
                TextButton(
                    onClick = onCancelClick,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "取消",
                        style = TextStyle(
                            fontSize = 18.sp,
                            color = Color.Black,
                            fontWeight = FontWeight.Bold
                        )
                    )
                }

                VerticalDivider(
                    color = Color.LightGray,
                    thickness = 0.5.dp,
                    modifier = Modifier.fillMaxHeight()
                        .width(1.dp)
                )
                
                // 确认按钮
                TextButton(
                    onClick = { onConfirmClick(selectedIndex, options[selectedIndex]) },
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "确定",
                        style = TextStyle(
                            fontSize = 18.sp,
                            color = LjRed,
                            fontWeight = FontWeight.Bold
                        )
                    )
                }
            }
        }
    }
}

/**
 * 多列操作选择器组件
 *
 * @param title 标题文本
 * @param columns 多列选项数据
 * @param initialSelectedIndices 初始选中的选项索引数组
 * @param onOptionSelected 选项被选中时的回调
 * @param onCancelClick 取消按钮点击事件
 * @param onConfirmClick 确定按钮点击事件
 */
@Composable
fun MultiColumnOperationSelector(
    modifier: Modifier = Modifier,
    title: String = "选择",
    columns: List<List<String>>,
    initialSelectedIndices: List<Int> = List(columns.size) { 0 },
    onOptionSelected: (columnIndex: Int, optionIndex: Int, option: String) -> Unit = { _, _, _ -> },
    onCancelClick: () -> Unit = {},
    onConfirmClick: (List<Int>, List<String>) -> Unit = { _, _ -> }
) {
    // 记住当前选中的索引
    var selectedIndices by remember { mutableStateOf(initialSelectedIndices.toMutableList()) }

    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 标题
            Text(
                text = title,
                style = TextStyle(fontSize = 18.sp, fontWeight = FontWeight.Bold),
                modifier = Modifier.padding(top = 30.dp, bottom = 5.dp)
            )

            // 多列选项容器
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(180.dp)
                    .padding(16.dp)
            ) {
                columns.forEachIndexed { columnIndex, options ->
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                    ) {
                            // 分隔线
                            HorizontalDivider(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .align(Alignment.TopCenter)
                                    .padding(top = 40.dp),
                                thickness = 0.5.dp,
                                color = Color.LightGray
                            )

                            HorizontalDivider(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .align(Alignment.TopCenter)
                                    .padding(top = 100.dp),
                                thickness = 0.5.dp,
                                color = Color.LightGray
                            )

                        // 单列选项列表
                        SingleColumnList(
                            options = options,
                            selectedIndex = selectedIndices.getOrElse(columnIndex) { 0 },
                            onSelectionChanged = { newIndex ->
                                selectedIndices = selectedIndices.toMutableList().apply {
                                    if (columnIndex < size) {
                                        this[columnIndex] = newIndex
                                    }
                                }
                                onOptionSelected(columnIndex, newIndex, options[newIndex])
                            }
                        )
                    }
                }
            }

            HorizontalDivider(thickness = 0.5.dp, color = Color.LightGray)

            // 底部按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(IntrinsicSize.Min),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 取消按钮
                TextButton(
                    onClick = onCancelClick,
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "取消",
                        style = TextStyle(
                            fontSize = 18.sp,
                            color = Color.Black,
                            fontWeight = FontWeight.Bold
                        )
                    )
                }

                VerticalDivider(
                    color = Color.LightGray,
                    thickness = 0.5.dp,
                    modifier = Modifier
                        .fillMaxHeight()
                        .width(1.dp)
                )

                // 确认按钮
                TextButton(
                    onClick = {
                        val selectedOptions = selectedIndices.mapIndexed { index, selectedIndex ->
                            columns.getOrNull(index)?.getOrNull(selectedIndex) ?: ""
                        }
                        onConfirmClick(selectedIndices, selectedOptions)
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "确定",
                        style = TextStyle(
                            fontSize = 18.sp,
                            color = LjRed,
                            fontWeight = FontWeight.Bold
                        )
                    )
                }
            }
        }
    }
}

/**
 * 单列选项列表组件
 */
@Composable
private fun SingleColumnList(
    options: List<String>,
    selectedIndex: Int,
    onSelectionChanged: (Int) -> Unit
) {
    val listState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()

    // 初始滚动到选中的项
    LaunchedEffect(selectedIndex) {
        if (selectedIndex >= 0 && selectedIndex < options.size) {
            listState.scrollToItem(selectedIndex)
        }
    }

    // 计算当前中心项
    val centerItemIndex by remember {
        derivedStateOf {
            val visibleItems = listState.layoutInfo.visibleItemsInfo
            if (visibleItems.isEmpty()) {
                0
            } else {
                // 中心位置是可见区域的中点
                val centerPosition = listState.layoutInfo.viewportEndOffset / 2f + listState.layoutInfo.viewportStartOffset / 2f

                // 查找最接近中心的项
                val centerItem = visibleItems.minByOrNull {
                    kotlin.math.abs((it.offset + it.size / 2f) - centerPosition)
                }

                centerItem?.index?.minus(1) ?: 0 // -1 是因为顶部有一个空白项
            }
        }
    }

    // 当中心项改变时更新选中项
    LaunchedEffect(centerItemIndex) {
        if (centerItemIndex >= 0 && centerItemIndex < options.size && centerItemIndex != selectedIndex) {
            onSelectionChanged(centerItemIndex)
        }
    }

    LazyColumn(
        state = listState,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxSize()
    ) {
        // 添加顶部空白项以确保第一个选项可以滚动到中间位置
        item { Spacer(modifier = Modifier.height(40.dp)) }

        // 显示选项
        items(options) { option ->
            val index = options.indexOf(option)
            val isSelected = index == selectedIndex

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(60.dp)
                    .padding(horizontal = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = option,
                    style = TextStyle(
                        fontSize = if (isSelected) 17.sp else 15.sp,
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                        color = if (isSelected) Color.Black else Color.Gray
                    ),
                    textAlign = TextAlign.Center
                )
            }
        }

        // 添加底部空白项以确保最后一个选项可以滚动到中间位置
        item { Spacer(modifier = Modifier.height(40.dp)) }
    }
}
